import {
  caseSearchColumns,
  contractSearchColumns,
  departmentSearchColumns,
  feeCodeSearchColumns
} from '../search-cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';

export const MoreInfomationTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Xử lí đối tượng p/b'
          name='processing_object'
          options={[
            { label: '1. Chỉ phân bổ trực tiếp cho các đối tượng khai báo', value: 1 },
            { label: '2. Chỉ phân bổ cho các đối tượng trung gian', value: 2 },
            { label: '3. <PERSON><PERSON> bổ cho cả đối tượng trực tiếp và trung gian', value: 3 }
          ]}
          disabled={formMode === 'view'}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Bộ phận'
          name='department'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Department'
          searchColumns={departmentSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='department_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Vụ việc'
          name='case'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Case'
          searchColumns={caseSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='case_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Mã phí'
          name='fee_code'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='FeeCode'
          searchColumns={feeCodeSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='fee_code_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Hợp đồng'
          name='contract'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Contract'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='contract_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Trạng thái'
          name='status'
          options={[
            { label: '1. Còn sử dụng', value: 1 },
            { label: '0. Không sử dụng', value: 0 }
          ]}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
