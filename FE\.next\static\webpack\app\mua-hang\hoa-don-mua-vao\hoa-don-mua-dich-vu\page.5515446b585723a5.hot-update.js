"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page",{

/***/ "(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BasicInfoTab: function() { return /* binding */ BasicInfoTab; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _cols_definition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cols-definition */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/cols-definition.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2.tsx\");\n/* harmony import */ var _components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm */ \"(app-pages-browser)/./src/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm.tsx\");\n/* harmony import */ var _components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom/arito/form/search-fields */ \"(app-pages-browser)/./src/components/custom/arito/form/search-fields/index.ts\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/Tabs.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/Tabs.tsx\");\n/* harmony import */ var _components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom/arito/form/radio-button */ \"(app-pages-browser)/./src/components/custom/arito/form/radio-button/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom/arito/form/form */ \"(app-pages-browser)/./src/components/custom/arito/form/form/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../schemas */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/schemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BasicInfoTab = (param)=>{\n    let { formMode, setShowTaxCodePopupForm, showTaxCodePopupForm } = param;\n    _s();\n    const [formType, setFormType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chung_tu\");\n    const { control } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__.AritoFormContext);\n    const isPaymentChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control,\n        name: \"payment\",\n        defaultValue: false\n    });\n    const isDiscountChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control,\n        name: \"discount\",\n        defaultValue: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-x-8 lg:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"w-32 text-sm font-medium\",\n                                        children: \"Giao dịch\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__.RadioButton, {\n                                            name: \"dataType\",\n                                            options: _schemas__WEBPACK_IMPORTED_MODULE_15__.transactionTypeOptions,\n                                            defaultValue: \"service\",\n                                            orientation: \"horizontal\",\n                                            onChange: ()=>{},\n                                            className: \"gap-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                style: {\n                                                    width: \"160px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                        type: \"checkbox\",\n                                                        name: \"payment\",\n                                                        disabled: formMode === \"view\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isPaymentChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Chi tiền\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 39\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isPaymentChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"select\",\n                                                name: \"paymentMethod\",\n                                                className: \"w-40\",\n                                                options: [\n                                                    {\n                                                        value: \"cash\",\n                                                        label: \"Tiền mặt\"\n                                                    },\n                                                    {\n                                                        value: \"bank\",\n                                                        label: \"Chuyển khoản\"\n                                                    },\n                                                    {\n                                                        value: \"credit\",\n                                                        label: \"Kh\\xe1c\"\n                                                    }\n                                                ],\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                style: {\n                                                    width: \"160px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                        type: \"checkbox\",\n                                                        name: \"discount\",\n                                                        disabled: formMode === \"view\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isDiscountChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Chiết khấu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 40\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isDiscountChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                        type: \"select\",\n                                                        name: \"discountType\",\n                                                        className: \"w-48\",\n                                                        options: [\n                                                            {\n                                                                value: \"byHand\",\n                                                                label: \"Tự nhập\"\n                                                            },\n                                                            {\n                                                                value: \"percent\",\n                                                                label: \"Giảm % theo h\\xf3a đơn\"\n                                                            },\n                                                            {\n                                                                value: \"fixed\",\n                                                                label: \"Giảm tiền tr\\xean tổng h\\xf3a đơn\"\n                                                            }\n                                                        ],\n                                                        disabled: formMode === \"view\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                        type: \"text\",\n                                                        name: \"discountValue\",\n                                                        className: \"w-16\",\n                                                        disabled: formMode === \"view\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                type: \"text\",\n                                label: \"M\\xe3 nh\\xe0 cung cấp\",\n                                name: \"supplierCode\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/supplier\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNCCSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__.Type1Tabs)({\n                                    formMode\n                                }),\n                                placeholder: \"Nhập v\\xe0 tra cứu\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                type: \"text\",\n                                label: \"T\\xean nh\\xe0 cung cấp\",\n                                name: \"supplierName\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"T\\xean nh\\xe0 cung cấp/đơn vị\",\n                                defaultValue: \"T\\xean nh\\xe0 cung cấp/đơn vị\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Địa chỉ\",\n                                name: \"address\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"Địa chỉ nh\\xe0 cung cấp\",\n                                defaultValue: \"Địa chỉ nh\\xe0 cung cấp\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"M\\xe3 nh\\xe2n vi\\xean\",\n                                name: \"employeeCode\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNVSearchColBasicInfo,\n                                searchEndpoint: \"hr/employee\",\n                                placeholder: \"sdfsd\",\n                                defaultValue: \"DSFSDFSD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"T\\xe0i khoản c\\xf3\",\n                                name: \"accountNumber\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/account\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.TKCoSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__.Type2Tabs)({\n                                    formMode\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Diễn giải\",\n                                name: \"description\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"M\\xe3 số thuế\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"text\",\n                                                name: \"taxCode\",\n                                                disabled: formMode === \"view\",\n                                                className: \"flex-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 15\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                onClick: ()=>setShowTaxCodePopupForm(true),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 58\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Dự c\\xf4ng nợ\",\n                                type: \"number\",\n                                name: \"debt\",\n                                disabled: formMode === \"view\",\n                                defaultValue: \"0\",\n                                inputClassName: \"text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Người giao h\\xe0ng\",\n                                type: \"text\",\n                                name: \"deliveryPerson\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"Nhập t\\xean người giao h\\xe0ng\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Email\",\n                                name: \"email\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__.PaymentTermSelectField, {\n                                name: \"paymentTerm\",\n                                label: \"Hạn thanh to\\xe1n\",\n                                formMode: formMode === \"view\" ? \"view\" : formMode === \"edit\" ? \"edit\" : \"add\",\n                                labelClassName: \"w-40\",\n                                inputClassName: \"flex-1\",\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                label: \"Số chứng từ\",\n                                withSearch: true,\n                                type: \"text\",\n                                name: \"bookNumber\",\n                                disabled: formMode === \"view\",\n                                searchEndpoint: \"accounting/transaction\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaCTSearchColBasicInfo,\n                                defaultValue: \"\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y chứng từ\",\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                type: \"date\",\n                                name: \"documentDate\",\n                                disabled: formMode === \"view\",\n                                defaultValue: \"2025-06-06\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Số h\\xf3a đơn\",\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                type: \"text\",\n                                name: \"invoiceNumber\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"SỐ H\\xd3A ĐƠN\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"K\\xfd hiệu\",\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                type: \"text\",\n                                name: \"invoiceSymbol\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"K\\xdd HIỆU\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y h\\xf3a đơn\",\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                type: \"date\",\n                                name: \"invoiceDate\",\n                                disabled: formMode === \"view\",\n                                defaultValue: \"2025-06-06\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Ngoại tệ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                name: \"foreignCurrency\",\n                                                type: \"select\",\n                                                disabled: formMode === \"view\",\n                                                className: \"w-20\",\n                                                defaultValue: \"VND\",\n                                                options: _schemas__WEBPACK_IMPORTED_MODULE_15__.currencyOptions\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                name: \"exchangeRate\",\n                                                type: \"text\",\n                                                disabled: formMode === \"view\",\n                                                className: \"flex-1\",\n                                                defaultValue: \"1.0000\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Trạng th\\xe1i\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Đ\\xe3 ghi sổ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_14__.Switch, {\n                                                defaultChecked: true,\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-[120px,1fr] items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Dữ liệu được nhận\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        name: \"dataReceived\",\n                                        type: \"checkbox\",\n                                        disabled: formMode === \"view\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            showTaxCodePopupForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                showTaxCodePopupForm: showTaxCodePopupForm,\n                setShowTaxCodePopupForm: setShowTaxCodePopupForm,\n                formMode: formMode,\n                currentObj: {}\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfoTab, \"009JD+HxJuQpJs08QjxCCt52q+A=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch\n    ];\n});\n_c = BasicInfoTab;\nvar _c;\n$RefreshReg$(_c, \"BasicInfoTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx\n"));

/***/ })

});