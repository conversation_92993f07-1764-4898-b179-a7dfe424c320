import { <PERSON><PERSON>, Printer, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import React from 'react';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { SearchFormValues } from '../schemas';

interface ActionBarProps {
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onFixedColumnsClick: () => void;
  onExportDataClick: () => void;
  onEditPrintTemplateClick: () => void;
  className?: string;
  searchParams?: SearchFormValues | null;
}

const ActionBar = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}: ActionBarProps) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Thẻ kho/Sổ chi tiết vật tư</h1>
        {searchParams && (
          <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
            <div className='mr-2 size-2 rounded-full bg-red-500' />
            {searchParams.ma_kho && `Kho: ${searchParams.ma_kho}, `}, Từ ngày: {searchParams.ngay_tu} đến ngày:{' '}
            {searchParams.ngay_den}
          </span>
        )}
      </div>
    }
    className={className}
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
    <AritoMenuButton
      title='In ấn'
      icon={<Printer />}
      items={[
        {
          title: 'Thẻ kho',
          icon: <AritoIcon icon={20} />,
          onClick: onEditPrintTemplateClick,
          group: 0
        },
        {
          title: 'Sổ chi tiết vật tư',
          icon: <AritoIcon icon={20} />,
          onClick: onEditPrintTemplateClick,
          group: 0
        }
      ]}
    />
    {onRefreshClick && <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
    )}
    {onExportDataClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
    )}
    <AritoMenuButton
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
