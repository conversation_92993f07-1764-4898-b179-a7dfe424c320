import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { EditPrintTemplatePrintColumns } from './PrintTab';
import { Label } from '@/components/ui/label';

export const EditPrintTemplateBasicInfo: React.FC = () => {
  return (
    <>
      <div className='p-3'>
        <div className='space-y-1'>
          <div className='flex items-center gap-1'>
            <Label className='w-40 min-w-40'>Loại giấy:</Label>
            <FormField
              name='paperType'
              type='select'
              options={[{ value: 'new_template', label: 'Thêm mới mẫu' }]}
              className='w-[114px]'
            />
            <span
              className='cursor-pointer border border-gray-200 px-2 py-1 text-sm font-medium text-gray-700 hover:text-blue-600'
              onClick={() => console.log('Xóa mẫu clicked')}
            >
              Xóa mẫu
            </span>
            <span
              className='ml-2 cursor-pointer border border-gray-200 px-2 py-1 text-sm font-medium text-gray-700 hover:text-blue-600'
              onClick={() => console.log('Sao chép mẫu clicked')}
            >
              Sao chép mẫu
            </span>
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Nhập tên mẫu mới:</Label>
            <FormField name='templateName' type='text' className='w-[114px]' />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tên khác:</Label>
            <FormField name='otherName' type='text' className='w-[114px]' />
          </div>

          <div className='flex items-center gap-4'>
            <div className='flex flex-1 items-center gap-1'>
              <Label className='w-40 min-w-40'>Mẫu:</Label>
              <FormField
                name='template'
                type='select'
                options={[
                  {
                    value: 'accountant_signed',
                    label: 'Mẫu kế toán trường ký'
                  },
                  {
                    value: 'accountant_director_signed',
                    label: 'Mẫu kể toán trường/giảm đốc ký'
                  },
                  { value: 'creator_signed', label: 'Mẫu người lập ký' }
                ]}
                className='w-[114px]'
              />
              <FormField
                name='paperOrientation'
                type='select'
                options={[
                  { value: 'landscape', label: 'Giấy in ngang' },
                  { value: 'portrait', label: 'Giấy in đứng' }
                ]}
                className='w-[130px]'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tiêu đề in:</Label>
            <FormField name='printTitle' type='text' className='w-[114px]' />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tiêu đề 2:</Label>
            <FormField name='printTitle2' type='text' className='w-[114px]' />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tiêu đề phụ:</Label>
            <FormField name='subTitle' type='text' className='w-[114px]' />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tiêu đề phụ 2:</Label>
            <FormField name='subTitle2' type='text' className='w-[114px]' />
          </div>

          <div className='flex flex-1 items-center gap-1'>
            <Label className='w-40 min-w-40'>Mẫu song ngữ:</Label>
            <FormField
              name='bilingualTemplate'
              type='select'
              options={[
                { value: 'true', label: 'Có' },
                { value: 'false', label: 'Không' }
              ]}
              className='w-[114px]'
            />
            <Label className='w-40 min-w-40'>In các dòng trống:</Label>
            <FormField
              name='printEmptyLines'
              type='select'
              options={[
                { value: 'false', label: 'Không in' },
                { value: 'true', label: 'Có in' }
              ]}
              className='w-[114px]'
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Loại mẫu:</Label>
            <FormField
              name='templateType'
              type='select'
              options={[
                {
                  value: 'current_user',
                  label: 'Mẫu cho người sử dụng hiện tại'
                },
                {
                  value: 'all_users',
                  label: 'Mẫu chung cho tất cà người dùng'
                }
              ]}
              className='w-[114px]'
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tên file được tạo:</Label>
            <FormField name='fileName' type='text' className='w-[114px]' />
          </div>
        </div>
      </div>

      <AritoHeaderTabs
        tabs={[
          {
            id: 'print-columns',
            label: 'Cột in',
            component: <EditPrintTemplatePrintColumns />
          }
        ]}
      />
    </>
  );
};
