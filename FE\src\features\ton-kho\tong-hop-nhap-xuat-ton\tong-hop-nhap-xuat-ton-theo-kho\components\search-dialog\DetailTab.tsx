import React from 'react';
import {
  giaoDichSearchColumns,
  itemSearchColumns,
  loSearchColumns,
  nhomVatTuSearchColumns,
  taiKhoanSearchColumns,
  viTriSearchColumns
} from '../../cols-definition';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab: React.FC<DetailTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-4 p-4'>
      {/* Mã vật tư */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã vật tư</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_vt'}
          columnDisplay={'ma_vt'}
          className='w-[11.25rem]'
          searchEndpoint='/materials/'
          searchColumns={itemSearchColumns}
          dialogTitle='Danh mục vật tư'
        />
      </div>
      {/* Tk vật tư */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tk vật tư</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_tk'}
          columnDisplay={'tk_vt'}
          className='w-[11.25rem]'
          searchEndpoint='/accounts/'
          searchColumns={taiKhoanSearchColumns}
          dialogTitle='Danh mục tài khoản'
        />
      </div>

      {/* Tk hạch toán */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tk hạch toán</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_tk'}
          columnDisplay={'tk_ht'}
          className='w-[11.25rem]'
          searchEndpoint='/accounts/'
          searchColumns={taiKhoanSearchColumns}
          dialogTitle='Danh mục tài khoản'
        />
      </div>

      {/* Tk giá vốn */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tk giá vốn</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_tk'}
          columnDisplay={'tk_gv'}
          className='w-[11.25rem]'
          searchEndpoint='/accounts/'
          searchColumns={taiKhoanSearchColumns}
          dialogTitle='Danh mục tài khoản'
        />
      </div>

      {/* Mã lô */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã lô</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_lo'}
          columnDisplay={'ma_lo'}
          className='w-[11.25rem]'
          searchEndpoint='/lo/'
          searchColumns={loSearchColumns}
          dialogTitle='Danh mục lô'
        />
      </div>

      {/* Mã vị trí */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã vị trí</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_vi_tri'}
          columnDisplay={'ma_vi_tri'}
          className='w-[11.25rem]'
          searchEndpoint='/locations/'
          searchColumns={viTriSearchColumns}
          dialogTitle='Danh mục vị trí'
        />
      </div>

      {/* Loại vật tư */}
      <div className='flex items-center gap-4'>
        <FormField
          name='loai_vt'
          type='select'
          label='Loại vật tư'
          labelClassName='text-sm min-w-[176px]'
          className='min-w-40'
          disabled={formMode === 'view'}
          options={[
            { label: 'Dịch vụ', value: '0' },
            { label: 'Vật tư', value: '1' },
            { label: 'Phụ tùng', value: '2' },
            { label: 'CCLĐ', value: '3' },
            { label: 'Bán thành phấm', value: '4' },
            { label: 'Thành phẩm', value: '5' },
            { label: 'Hàng hóa', value: '6' },
            { label: 'Hàng gia công', value: '7' }
          ]}
        />
        <FormField
          name='ton_kho_yn'
          type='checkbox'
          label='Chỉ xem vật tư có theo dõi tồn kho'
          labelClassName='text-sm'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Nhóm vật tư */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nhóm vật tư</Label>
        <SearchField
          type='text'
          displayRelatedField={'ma_nh'}
          columnDisplay={'nhom_vt1'}
          className='w-[11.25rem]'
          searchEndpoint='/groups/'
          searchColumns={nhomVatTuSearchColumns}
          dialogTitle='Nhóm vật tư 1'
        />
        <SearchField
          type='text'
          displayRelatedField={'ma_nh'}
          columnDisplay={'nhom_vt2'}
          className='w-[11.25rem]'
          searchEndpoint='/groups/'
          searchColumns={nhomVatTuSearchColumns}
          dialogTitle='Nhóm vật tư 2'
        />
        <SearchField
          type='text'
          displayRelatedField={'ma_nh'}
          columnDisplay={'nhom_vt33'}
          className='w-[11.25rem]'
          searchEndpoint='/groups/'
          searchColumns={nhomVatTuSearchColumns}
          dialogTitle='Nhóm vật tư 3'
        />
      </div>

      {/* Mẫu báo cáo */}
      <div className='flex items-center gap-4'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mẫu báo cáo</Label>
        <FormField
          name='mau_bc'
          type='select'
          className='min-w-44'
          options={[
            { label: 'Mẫu số lượng', value: '0' },
            { label: 'Mẫu số lượng và giá trị', value: '1' },
            { label: 'Mẫu số lượng và giá trị ngoại tệ', value: '2' }
          ]}
        />
      </div>
    </div>
  );
};

export default DetailTab;
