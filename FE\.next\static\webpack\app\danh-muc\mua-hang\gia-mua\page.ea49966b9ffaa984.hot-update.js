"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx":
/*!*************************************************************************************!*\
  !*** ./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var _constants___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/form/radio-button */ \"(app-pages-browser)/./src/components/custom/arito/form/radio-button/index.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BasicInfo = (param)=>{\n    let { formMode, searchFields } = param;\n    _s();\n    const { vatTu, setVatTu, donViTinh, setDonViTinh, nhaCungCap, setNhaCungCap, ngoaiTe, setNgoaiTe } = searchFields;\n    const isViewMode = formMode === \"view\";\n    const { setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext)();\n    const giaoDict = watch(\"giao_dich\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-h-[calc(100vh-150px)] overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 md:p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-y-4 md:gap-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 md:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Giao dịch\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_5__.RadioButton, {\n                                        name: \"giao_dich\",\n                                        options: [\n                                            {\n                                                value: \"service\",\n                                                label: \"DV. Mua dịch vụ\"\n                                            },\n                                            {\n                                                value: \"asset\",\n                                                label: \"TS. Mua t\\xe0i sản/c\\xf4ng cụ chi tiết\"\n                                            }\n                                        ],\n                                        defaultValue: giaoDict || \"service\",\n                                        onChange: (value)=>setValue(\"giao_dich\", value),\n                                        orientation: \"horizontal\",\n                                        className: \"flex flex-row gap-12\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"M\\xe3 vật tư\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_vt\",\n                                    columnDisplay: \"ma_vt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.VAT_TU, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.vatTuSearchColumns,\n                                    dialogTitle: \"Danh mục vật tư\",\n                                    value: (vatTu === null || vatTu === void 0 ? void 0 : vatTu.ma_vt) || \"\",\n                                    onRowSelection: setVatTu,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Đơn vị t\\xednh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_dvt\",\n                                    columnDisplay: \"dvt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.DON_VI_TINH, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.dvtSearchColumns,\n                                    dialogTitle: \"Danh mục đơn vị t\\xednh\",\n                                    value: (donViTinh === null || donViTinh === void 0 ? void 0 : donViTinh.dvt) || \"\",\n                                    onRowSelection: setDonViTinh,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ng\\xe0y hiệu lực\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"date\",\n                                    name: \"ngay_hieu_luc\",\n                                    className: \"w-40\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Nh\\xe0 cung cấp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"customer_name\",\n                                    columnDisplay: \"customer_code\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NHA_CUNG_CAP, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.khachHangSearchColumns,\n                                    dialogTitle: \"Danh mục nh\\xe0 cung cấp\",\n                                    value: (nhaCungCap === null || nhaCungCap === void 0 ? void 0 : nhaCungCap.customer_code) || \"\",\n                                    onRowSelection: setNhaCungCap,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ngoại tệ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_nt\",\n                                    columnDisplay: \"ma_nt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NGOAI_TE, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.ngoaiTeSearchColumns,\n                                    dialogTitle: \"Danh mục ngoại tệ\",\n                                    value: (ngoaiTe === null || ngoaiTe === void 0 ? void 0 : ngoaiTe.ma_nt) || \"\",\n                                    onRowSelection: setNgoaiTe,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Số lượng từ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"so_luong_tu\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Gi\\xe1 mua\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"gia_mua\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"trang_thai\",\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Trạng th\\xe1i\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    id: \"trang_thai\",\n                                    type: \"select\",\n                                    name: \"trang_thai\",\n                                    options: [\n                                        {\n                                            label: \"1. C\\xf2n sử dụng\",\n                                            value: 1\n                                        },\n                                        {\n                                            label: \"0. Kh\\xf4ng sử dụng\",\n                                            value: 0\n                                        }\n                                    ],\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfo, \"8gSdUFvM7fwO6r+A8M34gxoVIPM=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext\n    ];\n});\n_c = BasicInfo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BasicInfo);\nvar _c;\n$RefreshReg$(_c, \"BasicInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx\n"));

/***/ })

});