'use client';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import AritoConfirmModal from '@/components/custom/arito/confirm-modal';
import { nhapTuDongThanhPhamTuHoaDonSchema } from '../schemas';
import { AritoDataTables } from '@/components/custom/arito';
import { BasicInfoTab } from '../components/BasicInfoTab';
import { AritoDialog } from '@/components/custom/arito';
import { formatYyyyMmDd } from '@/lib/utils';

export default function NhapTuDongThanhPhamTuHoaDonPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<'tien_chuan' | 'ngoai_te'>('tien_chuan');

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  //Initial
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name // Use the name field as the id
    }))
  );

  const showErrorModal = (message: string) => {
    setConfirmModal({
      open: true,
      title: 'Lỗi',
      message: message,
      onConfirm: () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
      }
    });
  };

  const showSuccessModal = (message: string, onOk?: () => void) => {
    setConfirmModal({
      open: true,
      title: 'Thành công',
      message: message,
      onConfirm: () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedObj(order);
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = async (data: any) => {
    if (data.name) {
      console.log(`Name: ${data.name}`);
    } else {
      console.log('No name provided');
    }

    setSelectedTable(data.mau_bao_cao);

    // let [err, res] = [null, null];
    // switch (formMode) {
    //   case "add":
    //     [err, res] = await to(createObject("SoChiTietCongNo", data));
    //     break;
    //   case "edit":
    //     [err, res] = await to(updateObject("SoChiTietCongNo", data.name, data));
    //     break;
    // }

    // if (err) {
    //   console.log(err);
    //   showErrorModal(
    //     formMode === "add"
    //       ? "Tạo chi tiết công nợ thất bại"
    //       : "Cập nhật chi tiết công nợ thất bại",
    //   );
    //   return;
    // } else {
    //   console.log("OK");
    //   showSuccessModal(
    //     formMode === "add"
    //       ? "Tạo chi tiết công nợ thành công"
    //       : "Cập nhật chi tiết công nợ thành công",
    //     () => {
    //       window.location.reload();
    //     },
    //   );
    // }

    setShowForm(false);
  };

  const handleDelete = (obj: any) => {
    setConfirmModal({
      open: true,
      title: 'Cảnh báo',
      message: 'Bạn có chắc chắn muốn xoá chi tiết công nợ này không?',
      onConfirm: async () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        console.log('xoá');
        // Placeholder implementation - ERPNext functionality removed
        showSuccessModal('Xoá chi tiết công nợ thành công', () => {
          window.location.reload();
        });
      }
    });
  };

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      {/* <AritoDialog<any>
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title='Chi tiết công nợ'
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={nhapTuDongThanhPhamTuHoaDonSchema}
        headerFields={<BasicInfoTab formMode={formMode} />}
        maxWidth='md'
        fullWidth={true}
      /> */}
    </div>
  );
}
