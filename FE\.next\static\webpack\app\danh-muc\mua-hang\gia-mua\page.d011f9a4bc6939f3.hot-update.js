"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx":
/*!*************************************************************************************!*\
  !*** ./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var _constants___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst BasicInfo = (param)=>{\n    let { formMode, searchFields } = param;\n    _s();\n    const { vatTu, setVatTu, donViTinh, setDonViTinh, nhaCungCap, setNhaCungCap, ngoaiTe, setNgoaiTe } = searchFields;\n    const isViewMode = formMode === \"view\";\n    const { setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFormContext)();\n    const giaoDict = watch(\"giao_dich\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-h-[calc(100vh-150px)] overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 md:p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-y-4 md:gap-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 md:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Giao dịch\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row items-center\",\n                                        style: {\n                                            gap: \"48px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"giao_dich_service\",\n                                                        name: \"giao_dich\",\n                                                        value: \"service\",\n                                                        defaultChecked: giaoDict === \"service\" || !giaoDict,\n                                                        onChange: (e)=>setValue(\"giao_dich\", e.target.value),\n                                                        disabled: isViewMode,\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"giao_dich_service\",\n                                                        className: \"cursor-pointer text-sm\",\n                                                        children: \"DV. Mua dịch vụ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"giao_dich_asset\",\n                                                        name: \"giao_dich\",\n                                                        value: \"asset\",\n                                                        defaultChecked: giaoDict === \"asset\",\n                                                        onChange: (e)=>setValue(\"giao_dich\", e.target.value),\n                                                        disabled: isViewMode,\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"giao_dich_asset\",\n                                                        className: \"cursor-pointer text-sm\",\n                                                        children: \"TS. Mua t\\xe0i sản/c\\xf4ng cụ chi tiết\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"M\\xe3 vật tư\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_vt\",\n                                    columnDisplay: \"ma_vt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.VAT_TU, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.vatTuSearchColumns,\n                                    dialogTitle: \"Danh mục vật tư\",\n                                    value: (vatTu === null || vatTu === void 0 ? void 0 : vatTu.ma_vt) || \"\",\n                                    onRowSelection: setVatTu,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Đơn vị t\\xednh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_dvt\",\n                                    columnDisplay: \"dvt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.DON_VI_TINH, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.dvtSearchColumns,\n                                    dialogTitle: \"Danh mục đơn vị t\\xednh\",\n                                    value: (donViTinh === null || donViTinh === void 0 ? void 0 : donViTinh.dvt) || \"\",\n                                    onRowSelection: setDonViTinh,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ng\\xe0y hiệu lực\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"date\",\n                                    name: \"ngay_hieu_luc\",\n                                    className: \"w-40\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Nh\\xe0 cung cấp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"customer_name\",\n                                    columnDisplay: \"customer_code\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NHA_CUNG_CAP, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.khachHangSearchColumns,\n                                    dialogTitle: \"Danh mục nh\\xe0 cung cấp\",\n                                    value: (nhaCungCap === null || nhaCungCap === void 0 ? void 0 : nhaCungCap.customer_code) || \"\",\n                                    onRowSelection: setNhaCungCap,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ngoại tệ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_nt\",\n                                    columnDisplay: \"ma_nt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NGOAI_TE, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.ngoaiTeSearchColumns,\n                                    dialogTitle: \"Danh mục ngoại tệ\",\n                                    value: (ngoaiTe === null || ngoaiTe === void 0 ? void 0 : ngoaiTe.ma_nt) || \"\",\n                                    onRowSelection: setNgoaiTe,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Số lượng từ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"so_luong_tu\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Gi\\xe1 mua\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"gia_mua\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"trang_thai\",\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Trạng th\\xe1i\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    id: \"trang_thai\",\n                                    type: \"select\",\n                                    name: \"trang_thai\",\n                                    options: [\n                                        {\n                                            label: \"1. C\\xf2n sử dụng\",\n                                            value: 1\n                                        },\n                                        {\n                                            label: \"0. Kh\\xf4ng sử dụng\",\n                                            value: 0\n                                        }\n                                    ],\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfo, \"8gSdUFvM7fwO6r+A8M34gxoVIPM=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFormContext\n    ];\n});\n_c = BasicInfo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BasicInfo);\nvar _c;\n$RefreshReg$(_c, \"BasicInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx\n"));

/***/ })

});