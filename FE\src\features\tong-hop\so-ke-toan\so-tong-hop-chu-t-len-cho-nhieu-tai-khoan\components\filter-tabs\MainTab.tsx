import { FormField } from '@/components/custom/arito/form/form-field';

export const MainTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='date'
            label='Từ ngày'
            name='from_date'
          />
          <FormField className='grid grid-cols-[160px,1fr] items-center' type='date' label='Đến ngày' name='to_date' />
        </div>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='date'
          label='Ngày mở sổ'
          name='open_date'
        />
      </div>
    </div>
  </div>
);
