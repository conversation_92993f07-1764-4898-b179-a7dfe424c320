import { <PERSON><PERSON><PERSON>, Trash, <PERSON><PERSON>, <PERSON><PERSON>lus2, <PERSON>er, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onPrintClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onPrintMultipleClick?: () => void;
  onExportClick?: () => void;
  onExportExcelClick?: () => void;
  onImportExcelClick?: () => void;
  activeGroup?: string;
  group?: any[];
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onPrintClick,
  onSearchClick,
  onFixedColumnsClick,
  onRefreshClick,
  onPrintMultipleClick,
  onExportClick,
  onExportExcelClick,
  onImportExcelClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu xuất kho</h1>}>
      {onAddClick && <AritoActionButton title='Thêm' icon={FilePlus2} onClick={onAddClick} />}
      {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
      {onDeleteClick && (
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />
      )}
      {onCopyClick && (
        <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />
      )}

      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Phiếu xuất kho',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 0
          },
          {
            title: 'Phiếu xuất kho (A5)',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 0
          },
          {
            title: 'Phiếu xuất kho theo thông tư',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 0
          },
          {
            title: 'Chứng từ hạch toán',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 1
          },
          {
            title: 'Chứng từ hạch toán (ngoại tệ)',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 1
          },
          {
            title: 'Chứng từ hạch toán (song ngữ)',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 2
          },
          {
            title: 'Chứng từ hạch toán (ngoại tệ, song ngữ)',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 2
          }
        ]}
      />

      {onSearchClick && (
        <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} disabled={isViewDisabled} />
      )}

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: onFixedColumnsClick,
            group: 0
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={883} />,
            onClick: onPrintMultipleClick,
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportClick,
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: onExportExcelClick,
            group: 2
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: onImportExcelClick,
            group: 2
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
