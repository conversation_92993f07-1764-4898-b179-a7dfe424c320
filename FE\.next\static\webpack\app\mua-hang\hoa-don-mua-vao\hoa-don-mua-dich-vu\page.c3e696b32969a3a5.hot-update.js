"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page",{

/***/ "(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page/page.tsx":
/*!*********************************************************************************!*\
  !*** ./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page/page.tsx ***!
  \*********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ServicePurchaseInvoicePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_split__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-split */ \"(app-pages-browser)/./node_modules/react-split/dist/react-split.es.js\");\n/* harmony import */ var lodash_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/filter */ \"(app-pages-browser)/./node_modules/lodash/filter.js\");\n/* harmony import */ var lodash_filter__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_filter__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _cols_definition__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../cols-definition */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/cols-definition.tsx\");\n/* harmony import */ var _components_custom_arito_input_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/input-table */ \"(app-pages-browser)/./src/components/custom/arito/input-table/index.tsx\");\n/* harmony import */ var _components_custom_arito_colored_dot__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom/arito/colored-dot */ \"(app-pages-browser)/./src/components/custom/arito/colored-dot/index.tsx\");\n/* harmony import */ var _components_custom_arito_data_tables__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/custom/arito/data-tables */ \"(app-pages-browser)/./src/components/custom/arito/data-tables/index.tsx\");\n/* harmony import */ var _components_popup_from_popup_FromPopup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/popup/from-popup/FromPopup */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/popup/from-popup/FromPopup.tsx\");\n/* harmony import */ var _components_BasicInfoTab__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/BasicInfoTab */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _components_ActionBar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/ActionBar */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/ActionBar.tsx\");\n/* harmony import */ var _components_BottomBar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/BottomBar */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BottomBar.tsx\");\n/* harmony import */ var _components_DetailTab__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/DetailTab */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/DetailTab.tsx\");\n/* harmony import */ var _utils_Caculate__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../utils/Caculate */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/utils/Caculate.ts\");\n/* harmony import */ var _types_filterTabs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../types/filterTabs */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/types/filterTabs.tsx\");\n/* harmony import */ var _components_SearchForm__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../components/SearchForm */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/SearchForm.tsx\");\n/* harmony import */ var _components_OtherTab__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../components/OtherTab */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/OtherTab.tsx\");\n/* harmony import */ var _components_Tax__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../components/Tax */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/Tax.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../schemas */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/schemas.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ServicePurchaseInvoicePage() {\n    _s();\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formMode, setFormMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    const [selectedObj, setSelectedObj] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentObj, setCurrentObj] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [inputDetails, setInputDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [rows, setRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showFromPopUpForm, setShowFromPopUpForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTaxCodePopupForm, setShowTaxCodePopupForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSearchForm, setShowSearchForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCloseForm = ()=>{\n        setShowForm(false);\n    };\n    const handleOpenEditForm = (obj)=>{\n        setFormMode(\"edit\");\n        setCurrentObj(obj);\n        setInputDetails(obj.details || []);\n        setShowForm(true);\n    };\n    const handleOpenViewForm = (obj)=>{\n        setFormMode(\"view\");\n        setCurrentObj(obj);\n        setInputDetails(obj.details || []);\n        setShowForm(true);\n    };\n    const handleOpenAddForm = ()=>{\n        setFormMode(\"add\");\n        setCurrentObj(_schemas__WEBPACK_IMPORTED_MODULE_19__.purchaseServiceInvoiceInitialValues);\n        setInputDetails([]);\n        setShowForm(true);\n    };\n    const handleRowClick = (params)=>{\n        const obj = params.row;\n        setSelectedObj(obj);\n        setInputDetails(obj.details || []);\n    };\n    const handleFormSubmit = async (data)=>{};\n    const handleSearchSubmit = (data)=>{\n        console.log(\"Search criteria:\", data);\n        // TODO: Implement actual search logic here\n        // Mock search result\n        const filteredRows = rows.filter((row)=>{\n            // Apply search filters based on the data\n            return true; // Replace with actual filter logic\n        });\n        setRows(filteredRows);\n        setShowSearchForm(false);\n    };\n    const tables = [\n        {\n            name: \"Tất cả\",\n            rows: rows,\n            columns: (0,_cols_definition__WEBPACK_IMPORTED_MODULE_4__.exportMainColumns)(handleOpenViewForm, handleOpenEditForm)\n        },\n        ..._types_filterTabs__WEBPACK_IMPORTED_MODULE_15__.filterValues.map((filterValue)=>{\n            const filteredRows = lodash_filter__WEBPACK_IMPORTED_MODULE_3___default()(rows, (row)=>row.status === filterValue.value);\n            return {\n                name: filterValue.name,\n                rows: filteredRows,\n                columns: (0,_cols_definition__WEBPACK_IMPORTED_MODULE_4__.exportMainColumns)(handleOpenViewForm, handleOpenEditForm),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_colored_dot__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    color: filterValue.color,\n                    className: \"mr-2\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 15\n                }, this),\n                tabProps: {\n                    className: \"whitespace-nowrap\"\n                }\n            };\n        }),\n        ..._types_filterTabs__WEBPACK_IMPORTED_MODULE_15__.filterValues.length > 0 ? [] : []\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full min-h-screen w-screen flex-col lg:overflow-hidden\",\n        children: [\n            showForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto pb-[120px]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_10__.AritoForm, {\n                            mode: formMode,\n                            initialData: currentObj || undefined,\n                            onSubmit: handleFormSubmit,\n                            onClose: handleCloseForm,\n                            subTitle: \"H\\xf3a đơn mua dịch vụ\",\n                            headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BasicInfoTab__WEBPACK_IMPORTED_MODULE_9__.BasicInfoTab, {\n                                formMode: formMode,\n                                setShowTaxCodePopupForm: setShowTaxCodePopupForm,\n                                showTaxCodePopupForm: showTaxCodePopupForm\n                            }, void 0, false, void 0, void 0),\n                            tabs: [\n                                {\n                                    id: \"chi-tiet\",\n                                    label: \"Chi tiết\",\n                                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DetailTab__WEBPACK_IMPORTED_MODULE_13__.DetailTab, {\n                                        value: inputDetails,\n                                        onChange: setInputDetails,\n                                        formMode: formMode\n                                    }, void 0, false, void 0, void 0)\n                                },\n                                {\n                                    id: \"thue\",\n                                    label: \"Thuế\",\n                                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tax__WEBPACK_IMPORTED_MODULE_18__.TaxTab, {\n                                        value: inputDetails,\n                                        onChange: setInputDetails,\n                                        formMode: formMode\n                                    }, void 0, false, void 0, void 0)\n                                },\n                                {\n                                    id: \"file_dinh_kem\",\n                                    label: \"File đ\\xednh k\\xe8m\",\n                                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OtherTab__WEBPACK_IMPORTED_MODULE_17__.OtherTab, {\n                                        value: inputDetails,\n                                        onChange: setInputDetails,\n                                        formMode: formMode\n                                    }, void 0, false, void 0, void 0)\n                                }\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 left-0 right-0 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BottomBar__WEBPACK_IMPORTED_MODULE_12__.BottomBar, {\n                            totalQuantity: (0,_utils_Caculate__WEBPACK_IMPORTED_MODULE_14__.calculateTotals)(inputDetails).totalQuantity,\n                            totalExpense: (0,_utils_Caculate__WEBPACK_IMPORTED_MODULE_14__.calculateTotals)(inputDetails).totalExpense,\n                            totalTax: (0,_utils_Caculate__WEBPACK_IMPORTED_MODULE_14__.calculateTotals)(inputDetails).totalTax,\n                            totalAmount: (0,_utils_Caculate__WEBPACK_IMPORTED_MODULE_14__.calculateTotals)(inputDetails).totalAmount,\n                            totalPayment: 0,\n                            formMode: formMode\n                        }, void 0, false, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ActionBar__WEBPACK_IMPORTED_MODULE_11__.ActionBar, {\n                        onAddClick: handleOpenAddForm,\n                        onEditClick: ()=>selectedObj && handleOpenEditForm(selectedObj),\n                        onDeleteClick: ()=>{},\n                        onCopyClick: ()=>{},\n                        onSearchClick: ()=>setShowSearchForm(true),\n                        onRefreshClick: ()=>{},\n                        onPrintClick: ()=>{}\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_split__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"flex flex-1 flex-col overflow-hidden\",\n                        direction: \"vertical\",\n                        sizes: [\n                            50,\n                            50\n                        ],\n                        minSize: 200,\n                        gutterSize: 8,\n                        gutterAlign: \"center\",\n                        snapOffset: 30,\n                        dragInterval: 1,\n                        cursor: \"row-resize\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_data_tables__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    tables: tables,\n                                    onRowClick: handleRowClick,\n                                    selectedRowId: (selectedObj === null || selectedObj === void 0 ? void 0 : selectedObj.id) || undefined\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_input_table__WEBPACK_IMPORTED_MODULE_5__.AritoInputTable, {\n                                    value: (selectedObj === null || selectedObj === void 0 ? void 0 : selectedObj.details) || [],\n                                    columns: _cols_definition__WEBPACK_IMPORTED_MODULE_4__.exportBottomColumns,\n                                    tableActionButtons: [\n                                        \"export\",\n                                        \"pin\"\n                                    ],\n                                    mode: \"view\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            showFromPopUpForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_popup_from_popup_FromPopup__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                showFromPopUpForm: showFromPopUpForm,\n                setShowFromPopUpForm: setShowFromPopUpForm,\n                formMode: formMode,\n                currentObj: currentObj\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this),\n            showSearchForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchForm__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                open: showSearchForm,\n                onClose: ()=>setShowSearchForm(false),\n                onSubmit: handleSearchSubmit\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\page\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicePurchaseInvoicePage, \"zgwOWh1RRHb/nhxadvlJD0Km2dc=\");\n_c = ServicePurchaseInvoicePage;\nvar _c;\n$RefreshReg$(_c, \"ServicePurchaseInvoicePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page/page.tsx\n"));

/***/ })

});