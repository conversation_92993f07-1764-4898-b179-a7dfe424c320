import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { warehouseSearchColumns, yeuCauKiemKeColumns } from '../cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo = ({ formMode }: { formMode?: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến</Label>
          <div className='flex gap-2'>
            <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
          </div>
        </div>
      </div>

      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đơn vị</Label>
          <div>
            <FormField
              name='don_vi'
              type='select'
              className='w-[420px]'
              disabled={formMode === 'view'}
              options={[{ value: 0, label: 'CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MAI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }]}
            />
          </div>
        </div>
      </div>

      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã kho</Label>
          <SearchField
            type='text'
            displayRelatedField={'rgname'}
            columnDisplay={'rg_code'}
            className='w-[11.25rem]'
            searchEndpoint='/regions/'
            searchColumns={warehouseSearchColumns}
            dialogTitle='Danh mục tài khoản'
          />
        </div>
      </div>

      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số phiếu kiểm kê</Label>
          <SearchField
            type='text'
            displayRelatedField={'rgname'}
            columnDisplay={'rg_code'}
            className='w-[11.25rem]'
            searchEndpoint='/regions/'
            searchColumns={yeuCauKiemKeColumns}
            dialogTitle='Danh mục tài khoản'
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
