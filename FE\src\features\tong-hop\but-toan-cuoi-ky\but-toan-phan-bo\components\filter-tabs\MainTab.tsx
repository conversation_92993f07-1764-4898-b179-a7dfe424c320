import { useFormContext } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { unitSearchColumns } from '../../search-cols-definition';

type DateRangePresets = any;
type FieldConfig = any;
const DEFAULT_PRESETS: DateRangePresets = {
  thisMonth: 'Tháng này',
  prevMonth: 'Tháng trước',
  nextMonth: 'Tháng sau',
  thisQuarter: 'Quý này',
  prevQuarter: 'Quý trước',
  quarter1: 'Quý 1',
  quarter2: 'Quý 2',
  quarter3: 'Quý 3',
  quarter4: 'Quý 4',
  month1: 'Tháng 1',
  month2: 'Tháng 2',
  month3: 'Tháng 3',
  month4: 'Tháng 4',
  month5: 'Tháng 5',
  month6: 'Tháng 6',
  month7: 'Tháng 7',
  month8: 'Tháng 8',
  month9: 'Tháng 9',
  month10: 'Tháng 10',
  month11: 'Tháng 11',
  month12: 'Tháng 12',
  thisYear: 'Năm nay',
  prevYear: 'Năm trước'
};

// Define field configurations
const PERIOD_FIELDS: FieldConfig[] = [
  { id: 'from_period', type: 'number' },
  { id: 'to_period', type: 'number' },
  { id: 'year', type: 'number' }
];

export const MainTab = () => {
  const { setValue } = useFormContext();
  const [filterExpanded, setFilterExpanded] = useState(false);

  useEffect(() => {
    // Set default date range to current month and year
    const today = new Date();
    const currentMonth = today.getMonth() + 1; // 1-12
    const currentYear = today.getFullYear();

    setValue('from_period', currentMonth);
    setValue('to_period', currentMonth);
    setValue('year', currentYear);
  }, [setValue]);

  const updateFormValues = (fromPeriod: number, toPeriod: number, year?: number) => {
    setValue('from_period', fromPeriod);
    setValue('to_period', toPeriod);
    if (year !== undefined) {
      setValue('year', year);
    }
  };

  const handlePeriodPresetSelect = (preset: string) => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1; // 1-12

    switch (preset) {
      case 'today':
        // Current month
        updateFormValues(currentMonth, currentMonth, currentYear);
        break;

      case 'thisWeek':
        // Current month
        updateFormValues(currentMonth, currentMonth, currentYear);
        break;

      case 'thisMonth':
        updateFormValues(currentMonth, currentMonth, currentYear);
        break;

      case 'currentToDate':
        updateFormValues(currentMonth, currentMonth, currentYear);
        break;

      case 'prevMonth':
        const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        const prevMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear;
        updateFormValues(prevMonth, prevMonth, prevMonthYear);
        break;

      case 'nextMonth':
        const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
        const nextMonthYear = currentMonth === 12 ? currentYear + 1 : currentYear;
        updateFormValues(nextMonth, nextMonth, nextMonthYear);
        break;

      // Handle month presets
      case 'month1':
      case 'month2':
      case 'month3':
      case 'month4':
      case 'month5':
      case 'month6':
      case 'month7':
      case 'month8':
      case 'month9':
      case 'month10':
      case 'month11':
      case 'month12': {
        const monthNumber = parseInt(preset.replace('month', ''));
        updateFormValues(monthNumber, monthNumber, currentYear);
        break;
      }

      // Handle quarter presets
      case 'quarter1':
        updateFormValues(1, 3, currentYear);
        break;

      case 'quarter2':
        updateFormValues(4, 6, currentYear);
        break;

      case 'quarter3':
        updateFormValues(7, 9, currentYear);
        break;

      case 'quarter4':
        updateFormValues(10, 12, currentYear);
        break;

      case 'thisQuarter': {
        const thisQuarterStartMonth = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        updateFormValues(thisQuarterStartMonth, thisQuarterStartMonth + 2, currentYear);
        break;
      }

      case 'prevQuarter': {
        const thisQuarterStartMonth = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        let prevQuarterStartMonth = thisQuarterStartMonth - 3;
        let prevQuarterYear = currentYear;

        if (prevQuarterStartMonth <= 0) {
          prevQuarterStartMonth += 12;
          prevQuarterYear -= 1;
        }

        updateFormValues(prevQuarterStartMonth, prevQuarterStartMonth + 2, prevQuarterYear);
        break;
      }

      case 'quarterToDate': {
        const thisQuarterStartMonth = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        updateFormValues(thisQuarterStartMonth, currentMonth, currentYear);
        break;
      }

      case 'thisYear':
        updateFormValues(1, 12, currentYear);
        break;

      case 'prevYear':
        updateFormValues(1, 12, currentYear - 1);
        break;

      case 'yearToDate':
        updateFormValues(1, currentMonth, currentYear);
        break;

      default:
        return;
    }
  };

  // Updated handler to match the new AritoRangeSelect interface
  const handlePresetSelect = (
    preset: string,
    formatDate: (date: Date) => string,
    updateFieldValues: (fieldValues: Record<string, string>) => void,
    fields: FieldConfig[]
  ) => {
    // Process the preset
    const today = new Date();
    const currentMonth = today.getMonth() + 1; // 1-12
    const currentYear = today.getFullYear();

    // Initialize field values with default values
    let fromPeriod = currentMonth;
    let toPeriod = currentMonth;
    let year = currentYear;

    // Use the existing handler logic to determine period values
    switch (preset) {
      case 'thisMonth':
        // Current month (already set as default)
        break;

      case 'prevMonth':
        fromPeriod = currentMonth === 1 ? 12 : currentMonth - 1;
        toPeriod = fromPeriod;
        if (currentMonth === 1) {
          year = currentYear - 1;
        }
        break;

      case 'nextMonth':
        fromPeriod = currentMonth === 12 ? 1 : currentMonth + 1;
        toPeriod = fromPeriod;
        if (currentMonth === 12) {
          year = currentYear + 1;
        }
        break;

      // Handle month presets
      case 'month1':
      case 'month2':
      case 'month3':
      case 'month4':
      case 'month5':
      case 'month6':
      case 'month7':
      case 'month8':
      case 'month9':
      case 'month10':
      case 'month11':
      case 'month12': {
        fromPeriod = parseInt(preset.replace('month', ''));
        toPeriod = fromPeriod;
        break;
      }

      // Handle quarter presets
      case 'quarter1':
        fromPeriod = 1;
        toPeriod = 3;
        break;

      case 'quarter2':
        fromPeriod = 4;
        toPeriod = 6;
        break;

      case 'quarter3':
        fromPeriod = 7;
        toPeriod = 9;
        break;

      case 'quarter4':
        fromPeriod = 10;
        toPeriod = 12;
        break;

      case 'thisQuarter': {
        const thisQuarterStartMonth = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        fromPeriod = thisQuarterStartMonth;
        toPeriod = thisQuarterStartMonth + 2;
        break;
      }

      case 'prevQuarter': {
        const thisQuarterStartMonth = Math.floor((currentMonth - 1) / 3) * 3 + 1;
        let prevQuarterStartMonth = thisQuarterStartMonth - 3;

        if (prevQuarterStartMonth <= 0) {
          prevQuarterStartMonth += 12;
          year = currentYear - 1;
        }

        fromPeriod = prevQuarterStartMonth;
        toPeriod = prevQuarterStartMonth + 2;
        break;
      }

      case 'thisYear':
        fromPeriod = 1;
        toPeriod = 12;
        break;

      case 'prevYear':
        fromPeriod = 1;
        toPeriod = 12;
        year = currentYear - 1;
        break;

      default:
        // Keep default for unrecognized presets
        break;
    }

    // Create the field values object expected by AritoRangeSelect
    const fieldValues: Record<string, string> = {
      from_period: fromPeriod.toString(),
      to_period: toPeriod.toString(),
      year: year.toString()
    };

    // Use the component's updateFieldValues function
    updateFieldValues(fieldValues);

    // Also update our form directly
    updateFormValues(fromPeriod, toPeriod, year);
  };

  return (
    <div className='p-0'>
      <div className='grid grid-cols-1 gap-4'>
        <div className='space-y-0'>
          <div className='items-center gap-2 sm:grid sm:grid-cols-1 md:flex md:flex-row'>
            <div className='w-[100px] text-xs'>Kỳ từ/đến</div>
            <div className='flex items-center gap-3'>
              <FormField className='items-center' type='number' label='from_period' />
              <FormField className='items-center' type='number' label='to_period' />
              <div className='-ml-4 -mt-4 flex'>
                {/* <AritoRangeSelect
                  fields={PERIOD_FIELDS}
                  onPresetSelect={handlePresetSelect}
                  presets={DEFAULT_PRESETS}
                /> */}
              </div>
            </div>
          </div>
          <FormField className='items-center' type='number' label='Năm' name='year' />
          <FormField
            className='items-center'
            type='text'
            label='Đơn vị'
            name='unit'
            withSearch={true}
            searchEndpoint='Unit'
            searchColumns={unitSearchColumns}
            defaultSearchColumn='name'
            // displayRelatedField="unit_name"
          />
        </div>
      </div>
    </div>
  );
};
