import { vatTuSearchColumns, dvtSearchColumns, khachHangSearchColumns, ngoaiTeSearchColumns } from '@/constants/';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { VatTu, DonViTinh, DoiTuong, NgoaiTe } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';
import { FormMode } from '@/types/form';
import { useFormContext } from 'react-hook-form';

interface SearchFields {
  vatTu: VatTu | null;
  setVatTu: (vatTu: VatTu) => void;
  donViTinh: DonViTinh | null;
  setDonViTinh: (donViTinh: DonViTinh) => void;
  nhaCungCap: DoiTuong | null;
  setNhaCungCap: (nhaCungCap: DoiTuong) => void;
  ngoaiTe: NgoaiTe | null;
  setNgoaiTe: (ngoaiTe: NgoaiTe) => void;
}

interface BasicInfoProps {
  formMode: FormMode;
  searchFields: SearchFields;
}

const BasicInfo = ({ formMode, searchFields }: BasicInfoProps) => {
  const { vatTu, setVatTu, donViTinh, setDonViTinh, nhaCungCap, setNhaCungCap, ngoaiTe, setNgoaiTe } = searchFields;
  const isViewMode = formMode === 'view';
  const { setValue, watch } = useFormContext();
  const giaoDict = watch('giao_dich');

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-4 md:p-6'>
        <div className='flex flex-col gap-y-4 md:gap-y-6'>
          <div className='space-y-4 md:space-y-6'>
            {/* Giao dịch Radio Buttons */}
            <div className='flex flex-col sm:flex-row sm:items-center gap-4 mb-6'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Giao dịch</Label>
              <div className='flex-1'>
                <div className='flex flex-row items-center' style={{ gap: '48px' }}>
                  <div className='flex items-center space-x-2'>
                    <input
                      type='radio'
                      id='giao_dich_service'
                      name='giao_dich'
                      value='service'
                      defaultChecked={giaoDict === 'service' || !giaoDict}
                      onChange={(e) => setValue('giao_dich', e.target.value)}
                      disabled={isViewMode}
                      className='h-4 w-4'
                    />
                    <Label htmlFor='giao_dich_service' className='cursor-pointer text-sm'>
                      DV. Mua dịch vụ
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <input
                      type='radio'
                      id='giao_dich_asset'
                      name='giao_dich'
                      value='asset'
                      defaultChecked={giaoDict === 'asset'}
                      onChange={(e) => setValue('giao_dich', e.target.value)}
                      disabled={isViewMode}
                      className='h-4 w-4'
                    />
                    <Label htmlFor='giao_dich_asset' className='cursor-pointer text-sm'>
                      TS. Mua tài sản/công cụ chi tiết
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã vật tư</Label>
              <SearchField<VatTu>
                type='text'
                displayRelatedField='ten_vt'
                columnDisplay='ma_vt'
                searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
                searchColumns={vatTuSearchColumns}
                dialogTitle='Danh mục vật tư'
                value={vatTu?.ma_vt || ''}
                onRowSelection={setVatTu}
                disabled={isViewMode}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Đơn vị tính</Label>
              <SearchField<DonViTinh>
                type='text'
                displayRelatedField='ten_dvt'
                columnDisplay='dvt'
                searchEndpoint={`/${QUERY_KEYS.DON_VI_TINH}/`}
                searchColumns={dvtSearchColumns}
                dialogTitle='Danh mục đơn vị tính'
                value={donViTinh?.dvt || ''}
                onRowSelection={setDonViTinh}
                disabled={isViewMode}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ngày hiệu lực</Label>
              <FormField type='date' name='ngay_hieu_luc' className='w-40' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nhà cung cấp</Label>
              <SearchField<DoiTuong>
                type='text'
                displayRelatedField='customer_name'
                columnDisplay='customer_code'
                searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
                searchColumns={khachHangSearchColumns}
                dialogTitle='Danh mục nhà cung cấp'
                value={nhaCungCap?.customer_code || ''}
                onRowSelection={setNhaCungCap}
                disabled={isViewMode}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ngoại tệ</Label>
              <SearchField<NgoaiTe>
                type='text'
                displayRelatedField='ten_nt'
                columnDisplay='ma_nt'
                searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
                searchColumns={ngoaiTeSearchColumns}
                dialogTitle='Danh mục ngoại tệ'
                value={ngoaiTe?.ma_nt || ''}
                onRowSelection={setNgoaiTe}
                disabled={isViewMode}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Số lượng từ</Label>
              <FormField type='number' name='so_luong_tu' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Giá mua</Label>
              <FormField type='number' name='gia_mua' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='trang_thai' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Trạng thái
              </Label>
              <FormField
                id='trang_thai'
                type='select'
                name='trang_thai'
                options={[
                  { label: '1. Còn sử dụng', value: 1 },
                  { label: '0. Không sử dụng', value: 0 }
                ]}
                disabled={isViewMode}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
