'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { allocationJournalEntryDetailColumns, getAllocationJournalEntryColumns } from '../../cols-definition';
import { generalJournalFilterSchema, generalJournalSchema } from '../../schemas';
import AritoConfirmModal from '@/components/custom/arito/confirm-modal';
import AritoNotifyModal from '@/components/custom/arito/notify-modal';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { MainTab } from '../filter-tabs/MainTab';
import { ActionBar } from '../ActionBar';
export default function AllocationJournalEntryPage({ initialRows }: { initialRows: any[] }) {
  // Process initial data
  const processedRows = initialRows.map(row => ({
    ...row,
    id: row.name // Use the name field as the id
  }));

  // Initial form state
  const [initialFormSubmitted, setInitialFormSubmitted] = useState(false);
  const [showInitialForm, setShowInitialForm] = useState(true);
  const [initialFormData, setInitialFormData] = useState({
    from_date: '',
    to_date: '',
    account_type: '',
    journal_type: ''
  });

  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(processedRows.length > 0 ? processedRows[0] : null);
  // Track the selected row index for the DataGrid
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(processedRows.length > 0 ? '0' : null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  const [notifyModal, setNotifyModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });
  //Initial
  const [rows, setRows] = useState<any[]>(processedRows);

  // Debug selected row and row IDs
  useEffect(() => {
    console.log('Selected object:', selectedObj);
    console.log('Selected row index:', selectedRowIndex);
  }, [selectedObj, selectedRowIndex]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleCloseInitialForm = () => {
    // Allow closing the initial form without submission
    setShowInitialForm(false);
    // Don't set initialFormSubmitted to true, so nothing displays after closing
  };

  const handleInitialFormSubmit = (data: any) => {
    console.log('Initial form data:', data);
    // Process the form data here - you might want to fetch data based on these filters
    // For now, we're just accepting whatever they submitted
    setInitialFormData(data);
    setInitialFormSubmitted(true);
    setShowInitialForm(false);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const row = params.row as any;
    setSelectedObj(row);
    // The id in params.row is the index as a string
    setSelectedRowIndex(params.row.id.toString());
  };

  const showNotifyModal = (message: string, onOk?: () => void) => {
    setNotifyModal({
      open: true,
      message: message,
      title: 'Thông báo',
      onConfirm: () => {
        setNotifyModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const tables = [
    {
      name: 'Bút toán kế toán',
      rows: rows,
      columns: getAllocationJournalEntryColumns(handleOpenViewForm, () => {})
    },
    {
      name: 'Bút toán vụ việc, công trình',
      rows: rows,
      columns: getAllocationJournalEntryColumns(handleOpenViewForm, () => {})
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoNotifyModal
        open={notifyModal.open}
        onClose={() => setNotifyModal(prev => ({ ...prev, open: false }))}
        onConfirm={notifyModal.onConfirm}
        title={notifyModal.title}
        message={notifyModal.message}
      />

      {/* Initial Filter Form - Must be completed before seeing data */}
      {/* <AritoDialog<any>
        key='initial-filter-form'
        open={showInitialForm}
        onClose={handleCloseInitialForm}
        mode='add'
        title='Bút toán phân bổ'
        titleIcon={<AritoIcon icon={12} />}
        // initialData={initialFormData}
        onSubmit={handleInitialFormSubmit}
        schema={generalJournalFilterSchema}
        headerFields={
          <div className='flex flex-row gap-2'>
            <MainTab />
          </div>
        }
        maxWidth='md'
        fullWidth={true}
      /> */}

      {/* View form */}
      {/* <AritoDialog<any>
        key={`account-form-view-${showForm}`}
        open={showForm}
        onClose={handleCloseForm}
        mode='view'
        title='Xem bút toán phân bổ'
        titleIcon={<AritoIcon icon={12} />}
        initialData={currentObj || undefined}
        schema={generalJournalSchema}
        tabs={
          [
            // {
            //   id: "more",
            //   label: "Khác",
            //   component: <MoreInfomationTab formMode="view" />,
            // },
          ]
        }
        maxWidth='md'
        fullWidth={true}
      /> */}

      {/* Only show data tables and action bar after initial form is submitted */}
      {initialFormSubmitted && (
        <>
          <ActionBar onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)} isViewDisabled={!selectedObj} />
          <div className='h-[500px] w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              onRowClick={handleRowClick}
              selectedRowId={selectedRowIndex || undefined}
            />
          </div>
          <span className='text-lg font-medium'>Chi tiết hệ số phân bổ</span>
          <span className='pl-3 text-sm text-gray-500'>Hệ số phân bổ của bút toán số[]</span>
          <div className='h-[1px] w-full bg-gray-200'></div>
          <div className='h-[500px] w-full overflow-hidden'>
            <AritoInputTable
              value={selectedObj?.allocationJournalEntryDetails || []}
              columns={allocationJournalEntryDetailColumns}
              mode={formMode}
            />
          </div>
        </>
      )}
    </div>
  );
}
