import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SaveTemplateDialog } from '../../components';
import { Label } from '@/components/ui/label';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
  open: boolean;
  onClose: () => void;
  onOpen: () => void;
  onSave: (data: any) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode, open, onClose, onOpen, onSave }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Người lập</Label>
          <div className='w-full'>
            <FormField type='text' name='maker' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chọn báo cáo</Label>
          <div className='w-[400px]'>
            <FormField
              type='select'
              name='report_filter'
              disabled={formMode === 'view'}
              options={[
                { label: 'Báo cáo kết quả sản xuất kinh doanh- Thông tư 200', value: '1' },
                { label: 'PL 03-1A/TNDN: Kết quả hoạt động sản xuất kinh doanh', value: '2' }
              ]}
              defaultValue={'1'}
            />
          </div>
          <div className='h-9 w-9 flex-shrink-0'>
            {/* Fixed size container */}
            <RadixHoverDropdown
              iconNumber='...'
              items={[
                {
                  value: 'save',
                  label: 'Tạo mẫu phân tích mới',
                  icon: 7,
                  onClick: () => {
                    console.log('Saved new template');
                    onOpen();
                  }
                },
                {
                  value: 'edit',
                  label: 'Sửa mẫu đang chọn',
                  icon: 9,
                  onClick: () => console.log('edit current template')
                },
                {
                  value: 'delete',
                  label: 'Xóa mẫu đang chọn',
                  icon: 8,
                  onClick: () => console.log('Delete current template')
                }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='report_template'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn - năm', value: '1' },
                { label: 'Mẫu tiền chuẩn - giữa biên độ', value: '2' },
                { label: 'Mẫu tiền nt - năm', value: '3' },
                { label: 'Mẫu tiền nt - giữa biên độ', value: '4' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>
      </div>

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog open={open} onClose={onClose} onSave={onSave} templateType='filter' />
    </div>
  );
};
