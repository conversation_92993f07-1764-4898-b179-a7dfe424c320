import React, { useState, useEffect } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { xuLyChenhLechSoLieuKiemKeSchema } from '../schemas';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import BasicInfo from './BasicInfo';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [formData, setFormData] = useState({});

  const handleSubmit = (data: any) => {
    onSearch(data);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  return (
    <AritoDialog
      open={open}
      onClose={handleClose}
      title='Xử lý chênh lệch số liệu kiểm kê'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={xuLyChenhLechSoLieuKiemKeSchema}
        initialData={{}}
        onSubmit={handleSubmit}
        className='w-[800px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfo formMode={formMode} />
          </div>
        }
        bottomBar={<BottomBar mode={formMode} onSubmit={() => handleSubmit(formData)} onClose={onClose} />}
        classNameBottomBar='relative'
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
