import { Search, RefreshCw, FileText, Table, Printer } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onSearchClick: () => void;
  onEditPrintTemplateClick: () => void;
  onRefreshClick: () => void;
  onFixedColumnsClick: () => void;
  onExportDataClick: () => void;
}

const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onEditPrintTemplateClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick
}) => {
  return (
    <AritoActionBar
      titleComponent={<div className='text-lg font-semibold'> T<PERSON><PERSON> hợp nhập xuất tồn theo đơn vị tính </div>}
    >
      <AritoActionButton icon={Search} onClick={onSearchClick} title='Tìm kiếm' variant='primary' />
      <AritoActionButton icon={RefreshCw} onClick={onRefreshClick} title='Refresh' />
      <AritoActionButton icon={Table} onClick={onFixedColumnsClick} title='Cố định cột' />
      <AritoActionButton icon={FileText} onClick={onExportDataClick} title='Kết xuất dữ liệu' />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            icon: <AritoIcon icon={864} />,
            title: 'Chỉnh sửa mẫu in',
            onClick: () => {
              console.log('Chỉnh sửa mẫu in clicked');
              onEditPrintTemplateClick();
            }
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
