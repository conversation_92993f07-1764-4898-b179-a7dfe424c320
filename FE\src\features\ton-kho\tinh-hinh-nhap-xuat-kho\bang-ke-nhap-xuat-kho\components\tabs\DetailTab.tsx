import { useFormContext } from 'react-hook-form';
import React from 'react';
import {
  warehouseSearchColumns,
  materialSearchColumns,
  batchSearchColumns,
  locationSearchColumns,
  materialGroupSearchColumns
} from '../../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

const DetailTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='p-6'>
      <div className='grid grid-cols-1 gap-x-6 gap-y-1'>
        {/* Mã kho field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mã kho:</Label>
          <div className='ml-2 w-[64%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Danh mục kho'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              onValueChange={value => {
                setValue('ma_kho', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_kho', row.ma_kho);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã kho đ/c field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mã kho đ/c:</Label>
          <div className='ml-2 w-[64%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Danh mục kho'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              onValueChange={value => {
                setValue('ma_kho_dc', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_kho_dc', row.ma_kho);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vật tư field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mã vật tư:</Label>
          <div className='ml-2 w-[64%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={materialSearchColumns}
              dialogTitle='Danh mục vật tư'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              onValueChange={value => {
                setValue('ma_vat_tu', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vat_tu', row.ma_vt);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã lô field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mã lô:</Label>
          <div className='ml-2 w-[64%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={batchSearchColumns}
              dialogTitle='Danh mục lô'
              columnDisplay='ma_lo'
              displayRelatedField='ten_lo'
              onValueChange={value => {
                setValue('ma_lo', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_lo', row.ma_lo);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vị trí field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mã vị trí:</Label>
          <div className='ml-2 w-[64%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
              searchColumns={locationSearchColumns}
              dialogTitle='Danh mục vị trí'
              columnDisplay='ma_vi_tri'
              displayRelatedField='ten_vi_tri'
              onValueChange={value => {
                setValue('ma_vi_tri', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vi_tri', row.ma_vi_tri);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Loại vật tư field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left'>Loại vật tư:</Label>
          <div className='ml-2 flex w-full items-center gap-4'>
            <FormField
              name='loai_vat_tu'
              type='select'
              className='w-[170px]'
              inputClassName='w-full rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
              options={[
                { value: '0', label: 'Dịch vụ' },
                { value: '1', label: 'Vật tư' },
                { value: '2', label: 'Phụ tùng' },
                { value: '3', label: 'CCLĐ' },
                { value: '4', label: 'Bán thành phẩm' },
                { value: '5', label: 'Thành phẩm' },
                { value: '6', label: 'Hàng hóa' },
                { value: '7', label: 'Hàng gia công' }
              ]}
            />
            <FormField
              name='chi_vat_tu_ton_kho'
              type='checkbox'
              label='Chỉ xem vật tư có theo dõi tồn kho'
              labelClassName='text-sm font-medium text-gray-700'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Nhóm vật tư:</Label>
          <div className='ml-2 flex w-3/4 items-center gap-2'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={materialGroupSearchColumns}
              dialogTitle='Danh mục nhóm vật tư 1'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('ma_nhom_1', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhom_1', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={materialGroupSearchColumns}
              dialogTitle='Danh mục nhóm vật tư 2'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('ma_nhom_2', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhom_2', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={materialGroupSearchColumns}
              dialogTitle='Danh mục nhóm vật tư 3'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('ma_nhom_3', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhom_3', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto'
            />
          </div>
        </div>

        {/* Diễn giải field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left text-sm font-medium text-gray-700'>Diễn giải:</Label>
          <div className='ml-2 w-[54.5%]'>
            <FormField name='dien_giai' type='text' className='w-full' />
          </div>
        </div>

        {/* Mẫu báo cáo selection with dropdown */}
        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mẫu báo cáo</label>
          <div className='flex w-full items-center gap-2'>
            <div className='ml-2 w-2/3'>
              <FormField
                name='mau_bao_cao'
                type='select'
                className='w-full'
                options={[
                  { value: '0', label: 'Mẫu số lượng' },
                  { value: '1', label: 'Mẫu số lượng và giá trị' },
                  { value: '2', label: 'Mẫu số lượng và giá trị ngoại tệ' }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
