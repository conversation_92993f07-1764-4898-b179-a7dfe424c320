'use client';

import React from 'react';
import AritoConfirmModal from '@/components/custom/arito/confirm-modal';
import { xuLyChenhLechSoLieuKiemKeColumns } from './cols-definition';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, InitialSearchDialog } from './components';
import { useData, useDialogState } from './hooks';

export default function XuLyChenhLechSoLieuKiemKePage() {
  const {
    initialSearchDialogOpen,
    showTable,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    searchParams
  } = useDialogState();

  const { rows, selectedRowIndex, confirmModal, handleRowClick, handleRefreshClick, handleFixedColumnsClick } = useData(
    []
  );

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: xuLyChenhLechSoLieuKiemKeColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => confirmModal.onConfirm()}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            data={rows}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
