import { FormField } from '@/components/custom/arito/form/form-field';

export const MainTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='date'
            label='Từ ngày'
            name='from_date'
          />
          <FormField className='grid grid-cols-[160px,1fr] items-center' type='date' label='Đến ngày' name='to_date' />
        </div>
        <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Nh<PERSON><PERSON> theo'
            name='type'
            options={[
              { label: '<PERSON>h<PERSON><PERSON> hàng', value: 1 },
              { label: 'Đơn vị', value: 2 },
              { label: 'Tài khoản', value: 3 },
              { label: 'Tk đối ứng', value: 4 },
              { label: 'Bộ phận', value: 5 },
              { label: 'Vụ việc', value: 6 },
              { label: 'Hợp đồng', value: 7 },
              { label: 'Khế ước', value: 8 },
              { label: 'Phí', value: 9 },
              { label: 'Sản phẩm', value: 10 },
              { label: 'Lệnh sản xuất', value: 11 }
            ]}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='checkbox'
            label='Chỉ lấy DL có nhập'
            name='is_only_with_input'
          />
        </div>
      </div>
    </div>
  </div>
);
