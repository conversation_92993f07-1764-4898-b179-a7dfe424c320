import { useEffect, useRef, useState } from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';

// Dropdown menu component for reusability
const DropdownMenu = ({ isOpen, toggleDropdown }: { isOpen: boolean; toggleDropdown: (value: boolean) => void }) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        toggleDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [toggleDropdown]);

  return (
    <div className='relative' ref={dropdownRef}>
      <button
        className='flex items-center rounded border px-1.5 py-0.5 text-sm'
        type='button'
        onClick={() => toggleDropdown(!isOpen)}
      >
        <AritoIcon icon={12} />
      </button>
      {isOpen && (
        <div className='absolute right-0 z-10 mt-0.5 w-44 max-w-[calc(100vw-2rem)] rounded border bg-white text-sm shadow-lg'>
          <div className='py-1'>
            <button className='flex w-full items-center px-2 py-1.5 text-left hover:bg-gray-100 active:bg-gray-200'>
              <span className='mr-1 text-xs text-blue-500'>📝</span>
              Lưu mẫu mới
            </button>
            <button className='flex w-full items-center px-2 py-1.5 text-left hover:bg-gray-100 active:bg-gray-200'>
              <span className='mr-1 text-xs text-blue-500'>💾</span>
              Lưu đè vào mẫu đang chọn
            </button>
            <button className='flex w-full items-center px-2 py-1.5 text-left hover:bg-gray-100 active:bg-gray-200'>
              <span className='mr-1 text-xs text-red-500'>✖️</span>
              Xóa mẫu đang chọn
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export const MoreTab = () => {
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [showAnalysisDropdown, setShowAnalysisDropdown] = useState(false);

  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-4'>
        <div className='space-y-2'>
          <div className='flex gap-2'>
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='select'
              label='Mẫu lọc báo cáo'
              name='report_filter_type'
              options={[{ label: 'Người dùng tự lọc', value: 1 }]}
            />
            <DropdownMenu isOpen={showFilterDropdown} toggleDropdown={setShowFilterDropdown} />
          </div>

          <div className='flex gap-2'>
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='select'
              label='Mẫu phân tích DL'
              name='report_analysis_type'
              options={[{ label: 'Không phân tích', value: 0 }]}
            />
            <DropdownMenu isOpen={showAnalysisDropdown} toggleDropdown={setShowAnalysisDropdown} />
          </div>

          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            label='Người lập'
            name='creator'
          />
        </div>
      </div>
    </div>
  );
};
