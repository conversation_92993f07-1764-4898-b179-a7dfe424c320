import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '../../search-cols-definition';

export const DetailTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Tài khoản'
          name='account'
          type='text'
          withSearch={true}
          searchEndpoint='Account'
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='account_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Tính số dư'
          name='calculate_balance'
          options={[
            { label: 'Không', value: 0 },
            { label: 'Có', value: 1 }
          ]}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Loại'
          name='type'
          options={[
            { label: 'Tài khoản sổ cái', value: 1 },
            { label: 'Tài khoản chi tiết', value: 2 },
            { label: 'Tất cả', value: 3 }
          ]}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Mẫu báo cáo'
          name='report_template'
          options={[
            { label: 'Mẫu tiền chuẩn', value: 'standard_currency' },
            { label: 'Mẫu ngoại tệ', value: 'foreign_currency' }
          ]}
        />
      </div>
    </div>
  </div>
);
