import {
  agreementSearchColumns,
  caseSearchColumns,
  contractSearchColumns,
  departmentSearchColumns,
  feeSearchColumns,
  objectSearchColumns,
  paymentSearchColumns
} from '../../search-cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';

export const ObjectFilterTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        {/* Mã đối tượng */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Mã đối tượng'
          name='object_code'
          type='text'
          withSearch={true}
          searchEndpoint='Object'
          searchColumns={objectSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='object_name'
        />

        {/* <PERSON><PERSON> phận */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Bộ phận'
          name='department'
          type='text'
          withSearch={true}
          searchEndpoint='Department'
          searchColumns={departmentSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='department_name'
        />

        {/* Vụ việc */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Vụ việc'
          name='case'
          type='text'
          withSearch={true}
          searchEndpoint='Case'
          searchColumns={caseSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='case_name'
        />

        {/* Hợp đồng */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Hợp đồng'
          name='contract'
          type='text'
          withSearch={true}
          searchEndpoint='Contract'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='contract_name'
        />

        {/* Đợt thanh toán */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Đợt thanh toán'
          name='payment_period'
          type='text'
          withSearch={true}
          searchEndpoint='Payment'
          searchColumns={paymentSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='payment_name'
        />

        {/* Khế ước */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Khế ước'
          name='agreement'
          type='text'
          withSearch={true}
          searchEndpoint='Agreement'
          searchColumns={agreementSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='agreement_name'
        />

        {/* Phí */}
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Phí'
          name='cost'
          type='text'
          withSearch={true}
          searchEndpoint='Fee'
          searchColumns={feeSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='fee_name'
        />
      </div>
    </div>
  </div>
);
