import { FormField } from '@/components/custom/arito/form/form-field';

export const DetailTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        {/* Thông tin chi tiết */}
        <div className='space-y-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Mã vật tư'
            name='ma_vat_tu'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Mã lô'
            name='ma_lo'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Mã vị trí'
            name='ma_vi_tri'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <div className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[1fr,1fr]'>
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='select'
              label='Loại vật tư'
              name='loai_vat_tu'
              options={[
                { label: 'Dịch vụ', value: 0 },
                { label: 'Vật tư', value: 1 },
                { label: 'Phụ tùng', value: 2 },
                { label: 'CCLĐ', value: 3 },
                { label: 'Bán thành phẩm', value: 4 },
                { label: 'Thành phẩm', value: 5 },
                { label: 'Hàng hóa', value: 6 },
                { label: 'Hàng gia công', value: 7 }
              ]}
              disabled={formMode === 'view'}
            />
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='checkbox'
              label='Chỉ xem vật tư có theo dõi tồn kho'
              name='chi_xem_vat_tu_co_theo_doi_ton_kho'
              disabled={formMode === 'view'}
            />
          </div>
          <div className='grid grid-cols-[1fr] items-center gap-x-3 lg:grid lg:grid-cols-[98px,1fr,1fr,1fr]'>
            <span className='MuiTypography-root MuiTypography-body2 css-1j6aewf-MuiTypography-root'>Nhóm vật tư</span>
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='text'
              name='nhom_vat_tu_1'
              disabled={formMode === 'view'}
              withSearch={true}
            />
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='text'
              name='nhom_vat_tu_2'
              disabled={formMode === 'view'}
              withSearch={true}
            />
            <FormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='text'
              name='nhom_vat_tu_3'
              disabled={formMode === 'view'}
              withSearch={true}
            />
          </div>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Nhóm theo'
            name='nhom_theo'
            disabled={formMode === 'view'}
            options={[
              { label: 'Không phân nhóm', value: 0 },
              { label: 'Loại vật tư', value: 1 },
              { label: 'Nhóm vật tư 1', value: 2 },
              { label: 'Nhóm vật tư 2', value: 3 },
              { label: 'Nhóm vật tư 3', value: 4 },
              { label: 'Tài khoản vật tư', value: 5 }
            ]}
          />
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Mẫu báo cáo'
            name='mau_bao_cao'
            disabled={formMode === 'view'}
            options={[
              { label: 'Mẫu số lượng', value: 0 },
              { label: 'Mẫu số lượng và giá trị', value: 1 },
              { label: 'Mẫu số lượng và giá trị ngoại tệ', value: 2 }
            ]}
          />
        </div>
      </div>
    </div>
  );
};
