import { FormField } from '@/components/custom/arito/form/form-field';

export const MainTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='date'
            label='Từ ngày'
            name='from_date'
          />
          <FormField className='grid grid-cols-[160px,1fr] items-center' type='date' label='Đến ngày' name='to_date' />
        </div>
        <div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <FormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='<PERSON><PERSON>o c<PERSON>o theo'
            name='type'
            options={[
              { label: '<PERSON>h<PERSON>ch hàng', value: 1 },
              { label: 'Đơn vị', value: 2 },
              { label: 'T<PERSON><PERSON> kho<PERSON>n', value: 3 },
              { label: 'Tk đối ứng', value: 4 },
              { label: 'Bộ phận', value: 5 },
              { label: 'Vụ việc', value: 6 },
              { label: 'Hợp đồng', value: 7 },
              { label: 'Khế ước', value: 8 },
              { label: 'Phí', value: 9 },
              { label: 'Sản phẩm', value: 10 },
              { label: 'Lệnh sản xuất', value: 11 }
            ]}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            type='checkbox'
            label='Chỉ lấy DL có nhập'
            name='is_only_with_input'
          />
        </div>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Nhóm theo'
          name='type'
          options={[
            { label: 'Không nhóm', value: 1 },
            { label: 'Khách hàng', value: 2 },
            { label: 'Đơn vị', value: 3 },
            { label: 'Tài khoản', value: 4 },
            { label: 'Tk đối ứng', value: 5 },
            { label: 'Bộ phận', value: 6 },
            { label: 'Vụ việc', value: 7 },
            { label: 'Hợp đồng', value: 8 },
            { label: 'Khế ước', value: 9 },
            { label: 'Phí', value: 10 },
            { label: 'Sản phẩm', value: 11 },
            { label: 'Lệnh sản xuất', value: 12 }
          ]}
        />
      </div>
    </div>
  </div>
);
