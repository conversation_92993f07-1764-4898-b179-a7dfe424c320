import { FormField } from '@/components/custom/arito/form';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      {/* Cột trái */}
      <div className='col-span-4 space-y-2'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='select'
          options={[
            { value: 'NB', label: 'NB. Xuất nội bộ' },
            { value: 'XK', label: 'XK. Xuất khác' }
          ]}
          label='Giao dịch'
          name='requestNumber'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          label='Mã bộ phận'
          name='departmentCode'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          label='Người yêu cầu'
          name='requestPerson'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          label='Mã kho nhập'
          name='warehouseCode'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Diễn giải'
          name='description'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Cột phải */}
      <div className='col-span-4 space-y-2 lg:col-span-1'>
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
            label='Quyển'
            withSearch={true}
            type='text'
            name='bookNumber'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Số'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[40px,1fr]'
            type='text'
            name='dhmCode'
            disabled={formMode === 'view'}
          />
        </div>
        <FormField
          label='Ngày chứng từ'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          name='date'
          type='date'
          disabled={formMode === 'view'}
        />
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            label='Ngoại tệ'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
            type='text'
            name='foreignCurrency'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Tỷ giá'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[40px,1fr]'
            type='number'
            name='exchangeRate'
            disabled={formMode === 'view'}
          />
        </div>
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          name='status'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 'create', label: 'Lập chứng từ' },
            { value: 'pending', label: 'Chờ duyệt' },
            { value: 'approved', label: 'Chuyển vào SC' }
          ]}
        />
      </div>
    </div>
  </div>
);
