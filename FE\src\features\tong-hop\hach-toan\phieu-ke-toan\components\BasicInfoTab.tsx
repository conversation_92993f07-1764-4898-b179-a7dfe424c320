import { bookSearchColumns, currencySearchColumns } from '../search-cols-definition';
import { FormField } from '@/components/custom/arito/form';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-4 lg:grid-cols-5 lg:gap-x-8 lg:space-y-0'>
      {/* Cột trái */}
      <div className='col-span-4 space-y-2 sm:space-y-3'>
        <FormField
          className='grid grid-cols-[120px,1fr] items-baseline gap-y-1 sm:grid-cols-[160px,1fr] sm:items-center'
          label='Số chứng từ'
          name='documentNumber'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='AccountingVoucher'
          searchColumns={bookSearchColumns}
          defaultSearchColumn='documentNumber'
          displayRelatedField='documentNumber'
        />
        <FormField
          className='grid grid-cols-[120px,1fr] items-baseline sm:grid-cols-[160px,1fr] sm:items-center'
          label='Ngày chứng từ'
          name='documentDate'
          type='date'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[120px,1fr] items-baseline sm:grid-cols-[160px,1fr] sm:items-center'
          label='Diễn giải'
          name='description'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Cột phải */}
      <div className='col-span-4 lg:col-span-1'>
        <FormField
          className='grid grid-cols-[120px,1fr] items-baseline sm:grid-cols-[160px,1fr] sm:items-center lg:grid-cols-[120px,1fr]'
          label='Ngoại tệ'
          name='currency'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Currency'
          searchColumns={currencySearchColumns}
          defaultSearchColumn='currencyCode'
        />
        <FormField
          className='grid grid-cols-[120px,1fr] items-baseline sm:grid-cols-[160px,1fr] sm:items-center lg:grid-cols-[120px,1fr]'
          label='Tỷ giá'
          name='exchangeRate'
          type='number'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[120px,1fr] items-baseline sm:grid-cols-[160px,1fr] sm:items-center lg:grid-cols-[120px,1fr]'
          label='Trạng thái'
          name='status'
          type='select'
          disabled={formMode === 'view'}
          options={[{ value: 'recorded', label: 'Đã ghi sổ' }]}
        />
        <div className='flex w-full items-center gap-x-2 pl-[120px] sm:pl-[160px] lg:pl-[120px]'>
          <FormField type='checkbox' label='Dữ liệu được nhận' name='dataReceived' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  </div>
);
