import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Ng<PERSON>y từ/đến</Label>
          <AritoFormDateRangeDropdown fromDateName='date_from' toDateName='date_to' />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Ngày mở sổ</Label>
          <FormField type='date' name='open_date' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'></Label>
          <FormField
            type='checkbox'
            label='Chỉ lấy tài khoản có phát sinh'
            name='is_only_with_transaction'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};
