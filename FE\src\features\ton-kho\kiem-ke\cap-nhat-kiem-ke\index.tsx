'use client';

import React from 'react';
import { ActionBar, FormDialog, ConfirmDialog, InitialSearchDialog } from './components';
import AritoConfirmModal from '@/components/custom/arito/confirm-modal';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useFormState, useData, useDialogState } from './hooks';
import { capNhatKiemKeColumns } from './cols-definition';

export default function CapNhatKiemKePage() {
  const {
    showForm,
    showDelete,
    formMode,
    currentObj,
    handleCloseForm,
    handleCloseDelete,
    handleCloseCopy,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const {
    initialSearchDialogOpen,
    showTable,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    searchParams
  } = useDialogState();

  const {
    rows,
    selectedRowIndex,
    selectedObj,
    confirmModal,
    handleRowClick,
    handleFormSubmit,
    handleCopySubmit,
    handleDelete,
    showErrorModal,
    showSuccessModal,
    handleRefreshClick,
    handleFixedColumnsClick
  } = useData([]);

  const tables = [
    {
      name: '',
      rows: rows,
      columns: capNhatKiemKeColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => confirmModal.onConfirm()}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      {showTable && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onSearchClick={handleSearchClick}
            onEditClick={() => selectedObj && handleEditClick(selectedObj)}
            onDeleteClick={() => selectedObj && handleDelete(selectedObj)}
            onCopyClick={handleCopyClick}
            onViewClick={() => selectedObj && handleViewClick(selectedObj)}
            onFixedColumnsClick={handleFixedColumnsClick}
            onRefreshClick={handleRefreshClick}
            isEditDisabled={!selectedObj}
            isViewDisabled={!selectedObj}
            data={rows}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={() => handleDelete(selectedObj)}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn muốn xoá không?'
        />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onAdd={handleAddClick}
          onEdit={() => selectedObj && handleEditClick(selectedObj)}
          onDelete={() => selectedObj && handleDelete(selectedObj)}
          onCopy={handleCopyClick}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={currentObj}
        />
      )}
    </div>
  );
}
