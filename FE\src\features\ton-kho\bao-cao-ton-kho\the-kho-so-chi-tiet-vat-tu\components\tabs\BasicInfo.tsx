import { useFormContext } from 'react-hook-form';
import React from 'react';
import {
  batchSearchColumns,
  locationSearchColumns,
  materialGroupSearchColumns,
  materialSearchColumns,
  warehouseSearchColumns
} from '../../cols-definition';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

const BasicInfo: React.FC = () => {
  const { setValue } = useFormContext();
  return (
    <div className='p-6'>
      <div className='grid grid-cols-1 gap-x-6 gap-y-4'>
        {/* Ngày từ/đến field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left text-sm font-medium text-gray-700'>Ng<PERSON>y từ/đến:</Label>
          <div className='ml-2 w-[69%]'>
            <AritoFormDateRangeDropdown fromDateName='ngay_tu' toDateName='ngay_den' />
          </div>
        </div>

        {/* Mã kho field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left text-sm font-medium text-gray-700'>Mã kho:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Danh mục kho hàng'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              onValueChange={value => {
                setValue('ma_kho', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_kho', row.ma_kho);
                  setValue('ten_kho', row.ten_kho);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vật tư field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left text-sm font-medium text-gray-700'>Mã vật tư:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={materialSearchColumns}
              dialogTitle='Danh mục vật tư'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              onValueChange={value => {
                setValue('ma_vt', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vt', row.ma_vt);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Loại vật tư field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left text-sm font-medium text-gray-700'>Loại vật tư:</Label>
          <div className='ml-2 flex w-[69%] items-center gap-4'>
            <FormField
              name='loai_vat_tu'
              type='select'
              className='w-[210px]'
              inputClassName='w-full rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
              options={[
                { value: '0', label: 'Dịch vụ' },
                { value: '1', label: 'Vật tư' },
                { value: '2', label: 'Phụ tùng' },
                { value: '3', label: 'CCLĐ' },
                { value: '4', label: 'Bán thành phẩm' },
                { value: '5', label: 'Thành phẩm' },
                { value: '6', label: 'Hàng hóa' },
                { value: '7', label: 'Hàng gia công' }
              ]}
            />
            <FormField
              name='chi_vat_tu_ton_kho'
              type='checkbox'
              label='Chỉ xem vật tư có theo dõi tồn kho'
              labelClassName='text-sm font-medium text-gray-700'
            />
          </div>
        </div>

        {/* Nhóm vật tư field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left text-sm font-medium text-gray-700'>Nhóm vật tư:</Label>
          <div className='ml-2 flex w-[69%] items-center gap-2'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={materialGroupSearchColumns}
              dialogTitle='Danh mục nhóm vật tư 1'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('ma_nhom_1', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhom_1', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto'
              placeholder='Nhóm 1'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={materialGroupSearchColumns}
              dialogTitle='Danh mục nhóm vật tư 2'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('ma_nhom_2', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhom_2', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto'
              placeholder='Nhóm 2'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={materialGroupSearchColumns}
              dialogTitle='Danh mục nhóm vật tư 3'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              onValueChange={value => {
                setValue('ma_nhom_3', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhom_3', row.ma_nhom);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto'
              placeholder='Nhóm 3'
            />
          </div>
        </div>

        {/* Mẫu báo cáo selection with dropdown */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left text-sm font-medium text-gray-700'>Mẫu báo cáo:</Label>
          <div className='ml-2 w-[69%]'>
            <FormField
              name='mau_bao_cao'
              type='select'
              className='w-full'
              inputClassName='w-full rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
              options={[
                { value: '0', label: 'Thẻ kho' },
                { value: '1', label: 'Sổ chi tiết vật tư' },
                { value: '2', label: 'Sổ chi tiết vật tư tiền ngoại tệ' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
