import { useFormContext } from 'react-hook-form';
import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';

const BasicInfoTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Tài khoản */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản</Label>
          <div className='w-[70%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        {/* Date Range Fields */}
        <div className='flex w-[70%] items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_tu' toDateName='ngay_den' />
          </div>
        </div>
        {/* Date Open */}
        <div className='flex w-[70%] items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến</Label>
          <div>
            <FormField type='date' name='ngay_mo_so' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
