'use client';

import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { FormValues, FormSchema } from '../../schema';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: FormValues;
  selectedObj?: any | null;
  onSubmit?: (data: FormValues) => void;
  onClose: () => void;
}

const FormDialog = ({ open, onClose, onSubmit, formMode, initialData }: FormDialogProps) => {
  const handleSubmit = (data: FormValues) => {
    onSubmit?.(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Tính giá trung bình'}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={FormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode={formMode} />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
            >
              <AritoIcon icon={884} className='mr-2' />
              Đồng ý
            </Button>
            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} className='mr-2' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default FormDialog;
