import { FormField } from '@/components/custom/arito/form/form-field';
import { detailColumns } from '../cols-definition';
export const DetailTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 lg:grid-cols-5'>
      <div className='col-span-5 space-y-2'>
        <FormField label='Chi tiết' name='detail' type='table' columns={detailColumns} disabled={formMode === 'view'} />
      </div>
    </div>
  </div>
);
