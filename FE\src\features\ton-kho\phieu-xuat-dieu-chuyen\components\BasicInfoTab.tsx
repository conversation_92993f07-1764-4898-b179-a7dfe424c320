import { FormField } from '@/components/custom/arito/form';

export const BasicInfoTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      {/* Cột trái */}
      <div className='col-span-4 space-y-2'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='select'
          label='Số bước'
          name='stepNumber'
          disabled={formMode === 'view'}
          options={[
            { value: '1', label: '1. <PERSON>i<PERSON>u chuyển 1 bước' },
            { value: '2', label: '2. Đi<PERSON>u chuyển 2 bước' }
          ]}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='select'
          label='Giao dịch'
          name='transaction'
          disabled={formMode === 'view'}
          options={[
            { value: 'NB', label: 'NB. Điều chuyển nội bộ' },
            { value: 'DC', label: 'DC. Điều chỉnh' },
            { value: 'XK', label: 'XK. Khác' }
          ]}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          label='Mã kho xuất'
          name='warehouseCodeOut'
          type='text'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Diễn giải'
          name='description'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Cột phải */}
      <div className='col-span-4 space-y-2 lg:col-span-1'>
        <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-2 lg:space-y-0'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
            label='Quyển/Số'
            withSearch={true}
            type='text'
            name='bookNumber'
            disabled={formMode === 'view'}
          />
          <FormField
            label='PXDC'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[40px,1fr]'
            type='text'
            name='pxdcCode'
            disabled={formMode === 'view'}
          />
        </div>
        <FormField
          label='Ngày chứng từ'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          name='date'
          type='date'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Ngoại tệ'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          type='text'
          name='foreignCurrency'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tỷ giá'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[40px,1fr]'
          type='number'
          name='exchangeRate'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[60px,1fr]'
          name='status'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 'create', label: 'Lập chứng từ' },
            { value: 'pending', label: 'Chờ duyệt' },
            { value: 'approved', label: 'Xuất kho' }
          ]}
        />
        <div className='pl-[160px] lg:pl-[60px]'>
          <FormField
            type='checkbox'
            label='Chuyển dữ liệu'
            name='transferData'
            disabled={formMode === 'view'}
            className='flex items-center'
          />
        </div>
      </div>
    </div>
  </div>
);
