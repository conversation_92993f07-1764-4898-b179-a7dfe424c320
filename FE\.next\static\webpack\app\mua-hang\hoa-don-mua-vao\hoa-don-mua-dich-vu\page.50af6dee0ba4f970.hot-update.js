"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page",{

/***/ "(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BasicInfoTab: function() { return /* binding */ BasicInfoTab; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _cols_definition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cols-definition */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/cols-definition.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2.tsx\");\n/* harmony import */ var _components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm */ \"(app-pages-browser)/./src/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm.tsx\");\n/* harmony import */ var _components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom/arito/form/search-fields */ \"(app-pages-browser)/./src/components/custom/arito/form/search-fields/index.ts\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/Tabs.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/Tabs.tsx\");\n/* harmony import */ var _components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom/arito/form/radio-button */ \"(app-pages-browser)/./src/components/custom/arito/form/radio-button/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom/arito/form/form */ \"(app-pages-browser)/./src/components/custom/arito/form/form/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BasicInfoTab = (param)=>{\n    let { formMode, setShowTaxCodePopupForm, showTaxCodePopupForm } = param;\n    _s();\n    const [formType, setFormType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chung_tu\");\n    const { control } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__.AritoFormContext);\n    const isPaymentChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch)({\n        control,\n        name: \"payment\",\n        defaultValue: false\n    });\n    const isDiscountChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch)({\n        control,\n        name: \"discount\",\n        defaultValue: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-3 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 space-x-2 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[200px,1fr] items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm\",\n                                                children: \"Giao dịch\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__.RadioButton, {\n                                                name: \"dataType\",\n                                                options: [\n                                                    {\n                                                        value: \"service\",\n                                                        label: \"DV. Mua dịch vụ\"\n                                                    },\n                                                    {\n                                                        value: \"asset\",\n                                                        label: \"TS. Mua t\\xe0i sản/c\\xf4ng cụ\"\n                                                    }\n                                                ],\n                                                defaultValue: \"service\",\n                                                orientation: \"horizontal\",\n                                                onChange: ()=>{}\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-row\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            style: {\n                                                                width: \"160px\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    className: \"mr-2\",\n                                                                    type: \"checkbox\",\n                                                                    name: \"payment\",\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 64,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                !isPaymentChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Chi tiền\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 65,\n                                                                    columnNumber: 43\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        isPaymentChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                            type: \"select\",\n                                                            name: \"paymentMethod\",\n                                                            className: \"-ml-16 -mt-2 flex w-28 items-center\",\n                                                            options: [\n                                                                {\n                                                                    value: \"cash\",\n                                                                    label: \"Tiền mặt\"\n                                                                },\n                                                                {\n                                                                    value: \"bank\",\n                                                                    label: \"Chuyển khoản\"\n                                                                },\n                                                                {\n                                                                    value: \"credit\",\n                                                                    label: \"Kh\\xe1c\"\n                                                                }\n                                                            ],\n                                                            disabled: formMode === \"view\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            style: {\n                                                                width: \"160px\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    className: \"-ml-6\",\n                                                                    type: \"checkbox\",\n                                                                    name: \"discount\",\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Chiết khấu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 86,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        isDiscountChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"-ml-16 -mt-3 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    type: \"select\",\n                                                                    name: \"discountType\",\n                                                                    className: \"w-48\",\n                                                                    options: [\n                                                                        {\n                                                                            value: \"byHand\",\n                                                                            label: \"Tự nhập\"\n                                                                        },\n                                                                        {\n                                                                            value: \"percent\",\n                                                                            label: \"Giảm % theo h\\xf3a đơn\"\n                                                                        },\n                                                                        {\n                                                                            value: \"fixed\",\n                                                                            label: \"Giảm tiền tr\\xean tổng h\\xf3a đơn\"\n                                                                        }\n                                                                    ],\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 91,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    type: \"text\",\n                                                                    name: \"discountValue\",\n                                                                    className: \"w-12\",\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 105,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-x-20 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid grid-cols-[160px,1fr] items-center\",\n                                        type: \"text\",\n                                        label: \"M\\xe3 nh\\xe0 cung cấp\",\n                                        name: \"supplierCode\",\n                                        disabled: formMode === \"view\",\n                                        withSearch: true,\n                                        searchEndpoint: \"accounting/supplier\",\n                                        searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNCCSearchColBasicInfo,\n                                        actionButtons: [\n                                            \"add\",\n                                            \"edit\"\n                                        ],\n                                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            formMode: formMode\n                                        }, void 0, false, void 0, void 0),\n                                        tabs: (0,_components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__.Type1Tabs)({\n                                            formMode\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                                type: \"text\",\n                                                label: \"M\\xe3 số thuế\",\n                                                name: \"taxCode\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 15\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                onClick: ()=>setShowTaxCodePopupForm(true),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 58\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[160px,1fr] sm:items-center\",\n                                type: \"text\",\n                                label: \"T\\xean nh\\xe0 cung cấp\",\n                                name: \"supplierName\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"Địa chỉ\",\n                                name: \"address\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"M\\xe3 nh\\xe2n vi\\xean\",\n                                name: \"employeeCode\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNVSearchColBasicInfo,\n                                searchEndpoint: \"hr/employee\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"T\\xe0i khoản c\\xf3\",\n                                name: \"accountNumber\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/account\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.TKCoSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__.Type2Tabs)({\n                                    formMode\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"Diễn giải\",\n                                name: \"description\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center text-red-600 lg:grid-cols-[120px,1fr]\",\n                                label: \"Dư c\\xf4ng nợ\",\n                                type: \"number\",\n                                name: \"debt\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Người giao h\\xe0ng\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"deliveryPerson\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Email\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"email\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__.PaymentTermSelectField, {\n                                name: \"paymentTerm\",\n                                label: \"Hạn thanh to\\xe1n\",\n                                formMode: formMode === \"view\" ? \"view\" : formMode === \"edit\" ? \"edit\" : \"add\",\n                                labelClassName: \"w-32\",\n                                inputClassName: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                label: \"Số chứng từ\",\n                                withSearch: true,\n                                type: \"text\",\n                                name: \"bookNumber\",\n                                disabled: formMode === \"view\",\n                                searchEndpoint: \"accounting/transaction\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaCTSearchColBasicInfo\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y chứng từ\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"dhmCode\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Số h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"recipeNumber\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"K\\xfd hiệu\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"ky_hieu\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"nhd\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 items-center gap-x-4 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid w-3/4 grid-cols-[160px,1fr] items-center lg:grid-cols-[90px,1fr]\",\n                                        inputClassName: \"w-3/4\",\n                                        label: \"Ngoại tệ\",\n                                        name: \"foreignCurrency\",\n                                        type: \"select\",\n                                        disabled: formMode === \"view\",\n                                        options: [\n                                            {\n                                                value: \"VND\",\n                                                label: \"VND\"\n                                            },\n                                            {\n                                                value: \"USD\",\n                                                label: \"USD\"\n                                            },\n                                            {\n                                                value: \"EUR\",\n                                                label: \"EUR\"\n                                            },\n                                            {\n                                                value: \"JPY\",\n                                                label: \"JPY\"\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"flex-grow lg:ml-4\",\n                                        inputClassName: \"w-full\",\n                                        name: \"exchangeRate\",\n                                        type: \"text\",\n                                        disabled: formMode === \"view\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Trạng th\\xe1i\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"status\",\n                                type: \"select\",\n                                disabled: formMode === \"view\",\n                                options: [\n                                    {\n                                        value: \"create\",\n                                        label: \"Đ\\xe3 ghi sổ\"\n                                    },\n                                    {\n                                        value: \"pending\",\n                                        label: \"Chờ duyệt\"\n                                    },\n                                    {\n                                        value: \"approved\",\n                                        label: \"Chưa ghi sổ\"\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Dữ liệu được nhận\",\n                                className: \"grid grid-cols-[200px,1fr] items-center\",\n                                name: \"status\",\n                                type: \"checkbox\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            showTaxCodePopupForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                showTaxCodePopupForm: showTaxCodePopupForm,\n                setShowTaxCodePopupForm: setShowTaxCodePopupForm,\n                formMode: formMode,\n                currentObj: {}\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfoTab, \"009JD+HxJuQpJs08QjxCCt52q+A=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch\n    ];\n});\n_c = BasicInfoTab;\nvar _c;\n$RefreshReg$(_c, \"BasicInfoTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9tdWEtaGFuZy9ob2EtZG9uLW11YS12YW8vaG9hLWRvbi1tdWEtZGljaC12dS9jb21wb25lbnRzL0Jhc2ljSW5mb1RhYi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTZDO0FBQ0Y7QUFNZjtBQUNtRTtBQUNBO0FBQ0Q7QUFDUjtBQUNSO0FBQ0E7QUFDSjtBQUNIO0FBQ0Q7QUFDaEI7QUFPL0MsTUFBTWlCLGVBQWU7UUFBQyxFQUFFQyxRQUFRLEVBQUVDLHVCQUF1QixFQUFFQyxvQkFBb0IsRUFBUzs7SUFDN0YsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUd0QiwrQ0FBUUEsQ0FBd0M7SUFDaEYsTUFBTSxFQUFFdUIsT0FBTyxFQUFFLEdBQUd0QixpREFBVUEsQ0FBQ2EsaUZBQWdCQTtJQUUvQyxNQUFNVSxtQkFBbUJ0QiwwREFBUUEsQ0FBQztRQUNoQ3FCO1FBQ0FFLE1BQU07UUFDTkMsY0FBYztJQUNoQjtJQUVBLE1BQU1DLG9CQUFvQnpCLDBEQUFRQSxDQUFDO1FBQ2pDcUI7UUFDQUUsTUFBTTtRQUNOQyxjQUFjO0lBQ2hCO0lBRUEscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFNRCxXQUFVOzBEQUFVOzs7Ozs7MERBQzNCLDhEQUFDaEIsbUZBQVdBO2dEQUNWWSxNQUFLO2dEQUNMTSxTQUFTO29EQUNQO3dEQUFFQyxPQUFPO3dEQUFXRixPQUFPO29EQUFrQjtvREFDN0M7d0RBQUVFLE9BQU87d0RBQVNGLE9BQU87b0RBQTBCO2lEQUNwRDtnREFDREosY0FBYTtnREFDYk8sYUFBWTtnREFDWkMsVUFBVSxLQUFPOzs7Ozs7Ozs7Ozs7a0RBR3JCLDhEQUFDTjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzREQUFvQk0sT0FBTztnRUFBRUMsT0FBTzs0REFBUTs7OEVBQ3pELDhEQUFDckIsZ0ZBQVNBO29FQUFDYyxXQUFVO29FQUFPUSxNQUFLO29FQUFXWixNQUFLO29FQUFVYSxVQUFVcEIsYUFBYTs7Ozs7O2dFQUNqRixDQUFDTSxrQ0FBb0IsOERBQUNNO29FQUFNRCxXQUFVOzhFQUFzQjs7Ozs7Ozs7Ozs7O3dEQUc5REwsa0NBQ0MsOERBQUNULGdGQUFTQTs0REFDUnNCLE1BQUs7NERBQ0xaLE1BQUs7NERBQ0xJLFdBQVU7NERBQ1ZFLFNBQVM7Z0VBQ1A7b0VBQUVDLE9BQU87b0VBQVFGLE9BQU87Z0VBQVc7Z0VBQ25DO29FQUFFRSxPQUFPO29FQUFRRixPQUFPO2dFQUFlO2dFQUN2QztvRUFBRUUsT0FBTztvRUFBVUYsT0FBTztnRUFBTzs2REFDbEM7NERBQ0RRLFVBQVVwQixhQUFhOzs7Ozs7Ozs7Ozs7OERBSzdCLDhEQUFDVTtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzREQUFvQk0sT0FBTztnRUFBRUMsT0FBTzs0REFBUTs7OEVBQ3pELDhEQUFDckIsZ0ZBQVNBO29FQUFDYyxXQUFVO29FQUFRUSxNQUFLO29FQUFXWixNQUFLO29FQUFXYSxVQUFVcEIsYUFBYTs7Ozs7OzhFQUNwRiw4REFBQ1k7b0VBQU1ELFdBQVU7OEVBQXNCOzs7Ozs7Ozs7Ozs7d0RBR3hDRixtQ0FDQyw4REFBQ0M7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDZCxnRkFBU0E7b0VBQ1JzQixNQUFLO29FQUNMWixNQUFLO29FQUNMSSxXQUFVO29FQUNWRSxTQUFTO3dFQUNQOzRFQUFFQyxPQUFPOzRFQUFVRixPQUFPO3dFQUFVO3dFQUNwQzs0RUFBRUUsT0FBTzs0RUFBV0YsT0FBTzt3RUFBc0I7d0VBQ2pEOzRFQUNFRSxPQUFPOzRFQUNQRixPQUFPO3dFQUNUO3FFQUNEO29FQUNEUSxVQUFVcEIsYUFBYTs7Ozs7OzhFQUV6Qiw4REFBQ0gsZ0ZBQVNBO29FQUFDc0IsTUFBSztvRUFBT1osTUFBSztvRUFBZ0JJLFdBQVU7b0VBQU9TLFVBQVVwQixhQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPaEcsOERBQUNVO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2QsZ0ZBQVNBO3dDQUNSYyxXQUFVO3dDQUNWUSxNQUFLO3dDQUNMUCxPQUFNO3dDQUNOTCxNQUFLO3dDQUNMYSxVQUFVcEIsYUFBYTt3Q0FDdkJxQixZQUFZO3dDQUNaQyxnQkFBZTt3Q0FDZkMsZUFBZXRDLHFFQUF1QkE7d0NBQ3RDdUMsZUFBZTs0Q0FBQzs0Q0FBTzt5Q0FBTzt3Q0FDOUJDLDRCQUFjLDhEQUFDcEMscUdBQWlCQTs0Q0FBQ1csVUFBVUE7O3dDQUMzQzBCLE1BQU1qQywyRkFBU0EsQ0FBQzs0Q0FBRU87d0NBQVM7Ozs7OztrREFFN0IsOERBQUNVO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2QsZ0ZBQVNBO2dEQUNSYyxXQUFVO2dEQUNWUSxNQUFLO2dEQUNMUCxPQUFNO2dEQUNOTCxNQUFLO2dEQUNMYSxVQUFVcEIsYUFBYTs7Ozs7OzBEQUV6Qiw4REFBQ1U7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNiLGdFQUFTQTtvREFBQzZCLE1BQU07Ozs7Ozs7Ozs7OzBEQUVuQiw4REFBQ2pCO2dEQUFJQyxXQUFVO2dEQUFpQmlCLFNBQVMsSUFBTTNCLHdCQUF3QjswREFDckUsNEVBQUNILGdFQUFTQTtvREFBQzZCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUl2Qiw4REFBQzlCLGdGQUFTQTtnQ0FDUmMsV0FBVTtnQ0FDVlEsTUFBSztnQ0FDTFAsT0FBTTtnQ0FDTkwsTUFBSztnQ0FDTGEsVUFBVXBCLGFBQWE7Ozs7OzswQ0FFekIsOERBQUNILGdGQUFTQTtnQ0FDUmMsV0FBVTtnQ0FDVkMsT0FBTTtnQ0FDTkwsTUFBSztnQ0FDTFksTUFBSztnQ0FDTEMsVUFBVXBCLGFBQWE7Ozs7OzswQ0FFekIsOERBQUNILGdGQUFTQTtnQ0FDUmMsV0FBVTtnQ0FDVkMsT0FBTTtnQ0FDTkwsTUFBSztnQ0FDTFksTUFBSztnQ0FDTEMsVUFBVXBCLGFBQWE7Z0NBQ3ZCcUIsVUFBVTtnQ0FDVkUsZUFBZXJDLG9FQUFzQkE7Z0NBQ3JDb0MsZ0JBQWU7Ozs7OzswQ0FFakIsOERBQUN6QixnRkFBU0E7Z0NBQ1JjLFdBQVU7Z0NBQ1ZDLE9BQU07Z0NBQ05MLE1BQUs7Z0NBQ0xZLE1BQUs7Z0NBQ0xDLFVBQVVwQixhQUFhO2dDQUN2QnFCLFVBQVU7Z0NBQ1ZDLGdCQUFlO2dDQUNmQyxlQUFlcEMsb0VBQXNCQTtnQ0FDckNxQyxlQUFlO29DQUFDO29DQUFPO2lDQUFPO2dDQUM5QkMsNEJBQWMsOERBQUNuQyxxR0FBaUJBO29DQUFDVSxVQUFVQTs7Z0NBQzNDMEIsTUFBTWhDLDJGQUFTQSxDQUFDO29DQUFFTTtnQ0FBUzs7Ozs7OzBDQUU3Qiw4REFBQ0gsZ0ZBQVNBO2dDQUNSYyxXQUFVO2dDQUNWQyxPQUFNO2dDQUNOTCxNQUFLO2dDQUNMWSxNQUFLO2dDQUNMQyxVQUFVcEIsYUFBYTs7Ozs7Ozs7Ozs7O2tDQUszQiw4REFBQ1U7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDZCxnRkFBU0E7Z0NBQ1JjLFdBQVU7Z0NBQ1ZDLE9BQU07Z0NBQ05PLE1BQUs7Z0NBQ0xaLE1BQUs7Z0NBQ0xhLFVBQVVwQixhQUFhOzs7Ozs7MENBRXpCLDhEQUFDSCxnRkFBU0E7Z0NBQ1JlLE9BQU07Z0NBQ05ELFdBQVU7Z0NBQ1ZRLE1BQUs7Z0NBQ0xaLE1BQUs7Z0NBQ0xhLFVBQVVwQixhQUFhOzs7Ozs7MENBRXpCLDhEQUFDSCxnRkFBU0E7Z0NBQ1JlLE9BQU07Z0NBQ05ELFdBQVU7Z0NBQ1ZKLE1BQUs7Z0NBQ0xZLE1BQUs7Z0NBQ0xDLFVBQVVwQixhQUFhOzs7Ozs7MENBRXpCLDhEQUFDUiwrRkFBc0JBO2dDQUNyQmUsTUFBSztnQ0FDTEssT0FBTTtnQ0FDTlosVUFBVUEsYUFBYSxTQUFTLFNBQVNBLGFBQWEsU0FBUyxTQUFTO2dDQUN4RTZCLGdCQUFlO2dDQUNmQyxnQkFBZTs7Ozs7Ozs7Ozs7O2tDQUluQiw4REFBQ3BCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2QsZ0ZBQVNBO2dDQUNSYyxXQUFVO2dDQUNWQyxPQUFNO2dDQUNOUyxZQUFZO2dDQUNaRixNQUFLO2dDQUNMWixNQUFLO2dDQUNMYSxVQUFVcEIsYUFBYTtnQ0FDdkJzQixnQkFBZTtnQ0FDZkMsZUFBZW5DLG9FQUFzQkE7Ozs7OzswQ0FFdkMsOERBQUNTLGdGQUFTQTtnQ0FDUmUsT0FBTTtnQ0FDTkQsV0FBVTtnQ0FDVlEsTUFBSztnQ0FDTFosTUFBSztnQ0FDTGEsVUFBVXBCLGFBQWE7Ozs7OzswQ0FFekIsOERBQUNILGdGQUFTQTtnQ0FDUmUsT0FBTTtnQ0FDTkQsV0FBVTtnQ0FDVlEsTUFBSztnQ0FDTFosTUFBSztnQ0FDTGEsVUFBVXBCLGFBQWE7Ozs7OzswQ0FFekIsOERBQUNILGdGQUFTQTtnQ0FDUmUsT0FBTTtnQ0FDTkQsV0FBVTtnQ0FDVlEsTUFBSztnQ0FDTFosTUFBSztnQ0FDTGEsVUFBVXBCLGFBQWE7Ozs7OzswQ0FFekIsOERBQUNILGdGQUFTQTtnQ0FDUmUsT0FBTTtnQ0FDTkQsV0FBVTtnQ0FDVlEsTUFBSztnQ0FDTFosTUFBSztnQ0FDTGEsVUFBVXBCLGFBQWE7Ozs7OzswQ0FHekIsOERBQUNVO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2QsZ0ZBQVNBO3dDQUNSYyxXQUFVO3dDQUNWbUIsZ0JBQWU7d0NBQ2ZsQixPQUFNO3dDQUNOTCxNQUFLO3dDQUNMWSxNQUFLO3dDQUNMQyxVQUFVcEIsYUFBYTt3Q0FDdkJhLFNBQVM7NENBQ1A7Z0RBQUVDLE9BQU87Z0RBQU9GLE9BQU87NENBQU07NENBQzdCO2dEQUFFRSxPQUFPO2dEQUFPRixPQUFPOzRDQUFNOzRDQUM3QjtnREFBRUUsT0FBTztnREFBT0YsT0FBTzs0Q0FBTTs0Q0FDN0I7Z0RBQUVFLE9BQU87Z0RBQU9GLE9BQU87NENBQU07eUNBQzlCOzs7Ozs7a0RBRUgsOERBQUNmLGdGQUFTQTt3Q0FDUmMsV0FBVTt3Q0FDVm1CLGdCQUFlO3dDQUNmdkIsTUFBSzt3Q0FDTFksTUFBSzt3Q0FDTEMsVUFBVXBCLGFBQWE7Ozs7Ozs7Ozs7OzswQ0FHM0IsOERBQUNILGdGQUFTQTtnQ0FDUmUsT0FBTTtnQ0FDTkQsV0FBVTtnQ0FDVkosTUFBSztnQ0FDTFksTUFBSztnQ0FDTEMsVUFBVXBCLGFBQWE7Z0NBQ3ZCYSxTQUFTO29DQUNQO3dDQUFFQyxPQUFPO3dDQUFVRixPQUFPO29DQUFZO29DQUN0Qzt3Q0FBRUUsT0FBTzt3Q0FBV0YsT0FBTztvQ0FBWTtvQ0FDdkM7d0NBQUVFLE9BQU87d0NBQVlGLE9BQU87b0NBQWM7aUNBQzNDOzs7Ozs7MENBRUgsOERBQUNmLGdGQUFTQTtnQ0FDUmUsT0FBTTtnQ0FDTkQsV0FBVTtnQ0FDVkosTUFBSztnQ0FDTFksTUFBSztnQ0FDTEMsVUFBVXBCLGFBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQUs1QkUsc0NBQ0MsOERBQUNYLHFHQUFnQkE7Z0JBQ2ZXLHNCQUFzQkE7Z0JBQ3RCRCx5QkFBeUJBO2dCQUN6QkQsVUFBVUE7Z0JBQ1YrQixZQUFZLENBQUM7Ozs7Ozs7Ozs7OztBQUt2QixFQUFFO0dBbFNXaEM7O1FBSWNmLHNEQUFRQTtRQU1QQSxzREFBUUE7OztLQVZ2QmUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2ZlYXR1cmVzL211YS1oYW5nL2hvYS1kb24tbXVhLXZhby9ob2EtZG9uLW11YS1kaWNoLXZ1L2NvbXBvbmVudHMvQmFzaWNJbmZvVGFiLnRzeD82NTI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VXYXRjaCB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSc7XHJcbmltcG9ydCB7XHJcbiAgTWFOQ0NTZWFyY2hDb2xCYXNpY0luZm8sXHJcbiAgTWFOVlNlYXJjaENvbEJhc2ljSW5mbyxcclxuICBUS0NvU2VhcmNoQ29sQmFzaWNJbmZvLFxyXG4gIE1hQ1RTZWFyY2hDb2xCYXNpY0luZm9cclxufSBmcm9tICcuLi9jb2xzLWRlZmluaXRpb24nO1xyXG5pbXBvcnQgQmFzaWNJbmZvVGFiVHlwZTEgZnJvbSAnQC9jb21wb25lbnRzL2NhYy1sb2FpLWZvcm0vcG9wdXAtZm9ybS10eXBlLTEvQmFzaWNJbmZvVGFiVHlwZTEnO1xyXG5pbXBvcnQgQmFzaWNJbmZvVGFiVHlwZTIgZnJvbSAnQC9jb21wb25lbnRzL2NhYy1sb2FpLWZvcm0vcG9wdXAtZm9ybS10eXBlLTIvQmFzaWNJbmZvVGFiVHlwZTInO1xyXG5pbXBvcnQgVGF4Q29kZVBvcFVwRm9ybSBmcm9tICdAL2NvbXBvbmVudHMvY2FjLWxvYWktZm9ybS9hZGQtdGF4LWNvZGUtcG9wdXAvVGF4Q29kZVBvcFVwRm9ybSc7XHJcbmltcG9ydCB7IFBheW1lbnRUZXJtU2VsZWN0RmllbGQgfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvL2Zvcm0vc2VhcmNoLWZpZWxkcyc7XHJcbmltcG9ydCB7IFR5cGUxVGFicyB9IGZyb20gJ0AvY29tcG9uZW50cy9jYWMtbG9haS1mb3JtL3BvcHVwLWZvcm0tdHlwZS0xL1RhYnMnO1xyXG5pbXBvcnQgeyBUeXBlMlRhYnMgfSBmcm9tICdAL2NvbXBvbmVudHMvY2FjLWxvYWktZm9ybS9wb3B1cC1mb3JtLXR5cGUtMi9UYWJzJztcclxuaW1wb3J0IHsgUmFkaW9CdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvL2Zvcm0vcmFkaW8tYnV0dG9uJztcclxuaW1wb3J0IHsgQXJpdG9Gb3JtQ29udGV4dCB9IGZyb20gJ0AvY29tcG9uZW50cy9jdXN0b20vYXJpdG8vZm9ybS9mb3JtJztcclxuaW1wb3J0IHsgRm9ybUZpZWxkIH0gZnJvbSAnQC9jb21wb25lbnRzL2N1c3RvbS9hcml0by9mb3JtL2Zvcm0tZmllbGQnO1xyXG5pbXBvcnQgeyBBcml0b0ljb24gfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvJztcclxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnO1xyXG5pbnRlcmZhY2UgUHJvcHMge1xyXG4gIGZvcm1Nb2RlOiAnYWRkJyB8ICdlZGl0JyB8ICd2aWV3JztcclxuICBzaG93VGF4Q29kZVBvcHVwRm9ybTogYm9vbGVhbjtcclxuICBzZXRTaG93VGF4Q29kZVBvcHVwRm9ybTogKHZhbHVlOiBib29sZWFuKSA9PiB2b2lkO1xyXG59XHJcbmV4cG9ydCBjb25zdCBCYXNpY0luZm9UYWIgPSAoeyBmb3JtTW9kZSwgc2V0U2hvd1RheENvZGVQb3B1cEZvcm0sIHNob3dUYXhDb2RlUG9wdXBGb3JtIH06IFByb3BzKSA9PiB7XHJcbiAgY29uc3QgW2Zvcm1UeXBlLCBzZXRGb3JtVHlwZV0gPSB1c2VTdGF0ZTwnY2h1bmdfdHUnIHwgJ2hvYV9kb24nIHwgJ3BoaWV1X25oYXAnPignY2h1bmdfdHUnKTtcclxuICBjb25zdCB7IGNvbnRyb2wgfSA9IHVzZUNvbnRleHQoQXJpdG9Gb3JtQ29udGV4dCk7XHJcblxyXG4gIGNvbnN0IGlzUGF5bWVudENoZWNrZWQgPSB1c2VXYXRjaCh7XHJcbiAgICBjb250cm9sLFxyXG4gICAgbmFtZTogJ3BheW1lbnQnLFxyXG4gICAgZGVmYXVsdFZhbHVlOiBmYWxzZVxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBpc0Rpc2NvdW50Q2hlY2tlZCA9IHVzZVdhdGNoKHtcclxuICAgIGNvbnRyb2wsXHJcbiAgICBuYW1lOiAnZGlzY291bnQnLFxyXG4gICAgZGVmYXVsdFZhbHVlOiBmYWxzZVxyXG4gIH0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggcC00Jz5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEgZ2FwLXgtOCBzcGFjZS15LTIgbGc6Z3JpZC1jb2xzLTUgbGc6c3BhY2UteS0wJz5cclxuICAgICAgICB7LyogQ+G7mXQgdHLDoWkgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9J2NvbC1zcGFuLTMgc3BhY2UteS0yJz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy0xIHNwYWNlLXgtMiBsZzpncmlkLWNvbHMtMic+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy1bMjAwcHgsMWZyXSBpdGVtcy1jZW50ZXInPlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9J3RleHQtc20nPkdpYW8gZOG7i2NoPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8UmFkaW9CdXR0b25cclxuICAgICAgICAgICAgICAgIG5hbWU9J2RhdGFUeXBlJ1xyXG4gICAgICAgICAgICAgICAgb3B0aW9ucz17W1xyXG4gICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnc2VydmljZScsIGxhYmVsOiAnRFYuIE11YSBk4buLY2ggduG7pScgfSxcclxuICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJ2Fzc2V0JywgbGFiZWw6ICdUUy4gTXVhIHTDoGkgc+G6o24vY8O0bmcgY+G7pScgfVxyXG4gICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgIGRlZmF1bHRWYWx1ZT0nc2VydmljZSdcclxuICAgICAgICAgICAgICAgIG9yaWVudGF0aW9uPSdob3Jpem9udGFsJ1xyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IHt9fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBmbGV4LWNvbCc+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggZmxleC1yb3cnPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyJyBzdHlsZT17eyB3aWR0aDogJzE2MHB4JyB9fT5cclxuICAgICAgICAgICAgICAgICAgICA8Rm9ybUZpZWxkIGNsYXNzTmFtZT0nbXItMicgdHlwZT0nY2hlY2tib3gnIG5hbWU9J3BheW1lbnQnIGRpc2FibGVkPXtmb3JtTW9kZSA9PT0gJ3ZpZXcnfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgIHshaXNQYXltZW50Q2hlY2tlZCAmJiA8bGFiZWwgY2xhc3NOYW1lPSd0ZXh0LXNtIGZvbnQtbWVkaXVtJz5DaGkgdGnhu4FuPC9sYWJlbD59XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAge2lzUGF5bWVudENoZWNrZWQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxGb3JtRmllbGRcclxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9J3NlbGVjdCdcclxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU9J3BheW1lbnRNZXRob2QnXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9Jy1tbC0xNiAtbXQtMiBmbGV4IHctMjggaXRlbXMtY2VudGVyJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17W1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnY2FzaCcsIGxhYmVsOiAnVGnhu4FuIG3hurd0JyB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnYmFuaycsIGxhYmVsOiAnQ2h1eeG7g24ga2hv4bqjbicgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJ2NyZWRpdCcsIGxhYmVsOiAnS2jDoWMnIH1cclxuICAgICAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyJyBzdHlsZT17eyB3aWR0aDogJzE2MHB4JyB9fT5cclxuICAgICAgICAgICAgICAgICAgICA8Rm9ybUZpZWxkIGNsYXNzTmFtZT0nLW1sLTYnIHR5cGU9J2NoZWNrYm94JyBuYW1lPSdkaXNjb3VudCcgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT0ndGV4dC1zbSBmb250LW1lZGl1bSc+Q2hp4bq/dCBraOG6pXU8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHtpc0Rpc2NvdW50Q2hlY2tlZCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9Jy1tbC0xNiAtbXQtMyBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTInPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEZvcm1GaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPSdzZWxlY3QnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9J2Rpc2NvdW50VHlwZSdcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSd3LTQ4J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJ2J5SGFuZCcsIGxhYmVsOiAnVOG7sSBuaOG6rXAnIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJ3BlcmNlbnQnLCBsYWJlbDogJ0dp4bqjbSAlIHRoZW8gaMOzYSDEkcahbicgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogJ2ZpeGVkJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAnR2nhuqNtIHRp4buBbiB0csOqbiB04buVbmcgaMOzYSDEkcahbidcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtmb3JtTW9kZSA9PT0gJ3ZpZXcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxGb3JtRmllbGQgdHlwZT0ndGV4dCcgbmFtZT0nZGlzY291bnRWYWx1ZScgY2xhc3NOYW1lPSd3LTEyJyBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J30gLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEgZ2FwLXgtMjAgbGc6Z3JpZC1jb2xzLTInPlxyXG4gICAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy1bMTYwcHgsMWZyXSBpdGVtcy1jZW50ZXInXHJcbiAgICAgICAgICAgICAgdHlwZT0ndGV4dCdcclxuICAgICAgICAgICAgICBsYWJlbD0nTcOjIG5ow6AgY3VuZyBj4bqlcCdcclxuICAgICAgICAgICAgICBuYW1lPSdzdXBwbGllckNvZGUnXHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9XHJcbiAgICAgICAgICAgICAgd2l0aFNlYXJjaD17dHJ1ZX1cclxuICAgICAgICAgICAgICBzZWFyY2hFbmRwb2ludD0nYWNjb3VudGluZy9zdXBwbGllcidcclxuICAgICAgICAgICAgICBzZWFyY2hDb2x1bW5zPXtNYU5DQ1NlYXJjaENvbEJhc2ljSW5mb31cclxuICAgICAgICAgICAgICBhY3Rpb25CdXR0b25zPXtbJ2FkZCcsICdlZGl0J119XHJcbiAgICAgICAgICAgICAgaGVhZGVyRmllbGRzPXs8QmFzaWNJbmZvVGFiVHlwZTEgZm9ybU1vZGU9e2Zvcm1Nb2RlfSAvPn1cclxuICAgICAgICAgICAgICB0YWJzPXtUeXBlMVRhYnMoeyBmb3JtTW9kZSB9KX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGdhcC14LTInPlxyXG4gICAgICAgICAgICAgIDxGb3JtRmllbGRcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0nZ3JpZCBncmlkLWNvbHMtWzE2MHB4LDFmcl0gaXRlbXMtY2VudGVyJ1xyXG4gICAgICAgICAgICAgICAgdHlwZT0ndGV4dCdcclxuICAgICAgICAgICAgICAgIGxhYmVsPSdNw6Mgc+G7kSB0aHXhur8nXHJcbiAgICAgICAgICAgICAgICBuYW1lPSd0YXhDb2RlJ1xyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nY3Vyc29yLXBvaW50ZXInPlxyXG4gICAgICAgICAgICAgICAgPEFyaXRvSWNvbiBpY29uPXsxNX0gLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nY3Vyc29yLXBvaW50ZXInIG9uQ2xpY2s9eygpID0+IHNldFNob3dUYXhDb2RlUG9wdXBGb3JtKHRydWUpfT5cclxuICAgICAgICAgICAgICAgIDxBcml0b0ljb24gaWNvbj17NTh9IC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT0nZ3JpZCBncmlkLWNvbHMtWzEwMHB4LDFmcl0gaXRlbXMtc3RhcnQgZ2FwLXktMSBzbTpncmlkLWNvbHMtWzE2MHB4LDFmcl0gc206aXRlbXMtY2VudGVyJ1xyXG4gICAgICAgICAgICB0eXBlPSd0ZXh0J1xyXG4gICAgICAgICAgICBsYWJlbD0nVMOqbiBuaMOgIGN1bmcgY+G6pXAnXHJcbiAgICAgICAgICAgIG5hbWU9J3N1cHBsaWVyTmFtZSdcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZvcm1GaWVsZFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlcidcclxuICAgICAgICAgICAgbGFiZWw9J8SQ4buLYSBjaOG7iSdcclxuICAgICAgICAgICAgbmFtZT0nYWRkcmVzcydcclxuICAgICAgICAgICAgdHlwZT0ndGV4dCdcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZvcm1GaWVsZFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlcidcclxuICAgICAgICAgICAgbGFiZWw9J03DoyBuaMOibiB2acOqbidcclxuICAgICAgICAgICAgbmFtZT0nZW1wbG95ZWVDb2RlJ1xyXG4gICAgICAgICAgICB0eXBlPSd0ZXh0J1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgICAgd2l0aFNlYXJjaFxyXG4gICAgICAgICAgICBzZWFyY2hDb2x1bW5zPXtNYU5WU2VhcmNoQ29sQmFzaWNJbmZvfVxyXG4gICAgICAgICAgICBzZWFyY2hFbmRwb2ludD0naHIvZW1wbG95ZWUnXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZvcm1GaWVsZFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlcidcclxuICAgICAgICAgICAgbGFiZWw9J1TDoGkga2hv4bqjbiBjw7MnXHJcbiAgICAgICAgICAgIG5hbWU9J2FjY291bnROdW1iZXInXHJcbiAgICAgICAgICAgIHR5cGU9J3RleHQnXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtmb3JtTW9kZSA9PT0gJ3ZpZXcnfVxyXG4gICAgICAgICAgICB3aXRoU2VhcmNoXHJcbiAgICAgICAgICAgIHNlYXJjaEVuZHBvaW50PSdhY2NvdW50aW5nL2FjY291bnQnXHJcbiAgICAgICAgICAgIHNlYXJjaENvbHVtbnM9e1RLQ29TZWFyY2hDb2xCYXNpY0luZm99XHJcbiAgICAgICAgICAgIGFjdGlvbkJ1dHRvbnM9e1snYWRkJywgJ2VkaXQnXX1cclxuICAgICAgICAgICAgaGVhZGVyRmllbGRzPXs8QmFzaWNJbmZvVGFiVHlwZTIgZm9ybU1vZGU9e2Zvcm1Nb2RlfSAvPn1cclxuICAgICAgICAgICAgdGFicz17VHlwZTJUYWJzKHsgZm9ybU1vZGUgfSl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZvcm1GaWVsZFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlcidcclxuICAgICAgICAgICAgbGFiZWw9J0Rp4buFbiBnaeG6o2knXHJcbiAgICAgICAgICAgIG5hbWU9J2Rlc2NyaXB0aW9uJ1xyXG4gICAgICAgICAgICB0eXBlPSd0ZXh0J1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBD4buZdCBnaeG7r2EgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9J2NvbC1zcGFuLTQgZ3JpZCBncmlkLWNvbHMtMSBzcGFjZS15LTEgbGc6Y29sLXNwYW4tMSc+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT0nZ3JpZCBncmlkLWNvbHMtWzE2MHB4LDFmcl0gaXRlbXMtY2VudGVyIHRleHQtcmVkLTYwMCBsZzpncmlkLWNvbHMtWzEyMHB4LDFmcl0nXHJcbiAgICAgICAgICAgIGxhYmVsPSdExrAgY8O0bmcgbuG7oydcclxuICAgICAgICAgICAgdHlwZT0nbnVtYmVyJ1xyXG4gICAgICAgICAgICBuYW1lPSdkZWJ0J1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGxhYmVsPSdOZ8aw4budaSBnaWFvIGjDoG5nJ1xyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlciBsZzpncmlkLWNvbHMtWzEyMHB4LDFmcl0nXHJcbiAgICAgICAgICAgIHR5cGU9J3RleHQnXHJcbiAgICAgICAgICAgIG5hbWU9J2RlbGl2ZXJ5UGVyc29uJ1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGxhYmVsPSdFbWFpbCdcclxuICAgICAgICAgICAgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy1bMTYwcHgsMWZyXSBpdGVtcy1jZW50ZXIgbGc6Z3JpZC1jb2xzLVsxMjBweCwxZnJdJ1xyXG4gICAgICAgICAgICBuYW1lPSdlbWFpbCdcclxuICAgICAgICAgICAgdHlwZT0ndGV4dCdcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPFBheW1lbnRUZXJtU2VsZWN0RmllbGRcclxuICAgICAgICAgICAgbmFtZT0ncGF5bWVudFRlcm0nXHJcbiAgICAgICAgICAgIGxhYmVsPSdI4bqhbiB0aGFuaCB0b8OhbidcclxuICAgICAgICAgICAgZm9ybU1vZGU9e2Zvcm1Nb2RlID09PSAndmlldycgPyAndmlldycgOiBmb3JtTW9kZSA9PT0gJ2VkaXQnID8gJ2VkaXQnIDogJ2FkZCd9XHJcbiAgICAgICAgICAgIGxhYmVsQ2xhc3NOYW1lPSd3LTMyJ1xyXG4gICAgICAgICAgICBpbnB1dENsYXNzTmFtZT0ndy00OCdcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPSdjb2wtc3Bhbi00IGdyaWQgZ3JpZC1jb2xzLTEgc3BhY2UteS0xIGxnOmNvbC1zcGFuLTEnPlxyXG4gICAgICAgICAgPEZvcm1GaWVsZFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlciBsZzpncmlkLWNvbHMtWzEyMHB4LDFmcl0nXHJcbiAgICAgICAgICAgIGxhYmVsPSdT4buRIGNo4bupbmcgdOG7qydcclxuICAgICAgICAgICAgd2l0aFNlYXJjaD17dHJ1ZX1cclxuICAgICAgICAgICAgdHlwZT0ndGV4dCdcclxuICAgICAgICAgICAgbmFtZT0nYm9va051bWJlcidcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Zvcm1Nb2RlID09PSAndmlldyd9XHJcbiAgICAgICAgICAgIHNlYXJjaEVuZHBvaW50PSdhY2NvdW50aW5nL3RyYW5zYWN0aW9uJ1xyXG4gICAgICAgICAgICBzZWFyY2hDb2x1bW5zPXtNYUNUU2VhcmNoQ29sQmFzaWNJbmZvfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGb3JtRmllbGRcclxuICAgICAgICAgICAgbGFiZWw9J05nw6B5IGNo4bupbmcgdOG7qydcclxuICAgICAgICAgICAgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy1bMTYwcHgsMWZyXSBpdGVtcy1jZW50ZXIgbGc6Z3JpZC1jb2xzLVsxMjBweCwxZnJdJ1xyXG4gICAgICAgICAgICB0eXBlPSdkYXRlJ1xyXG4gICAgICAgICAgICBuYW1lPSdkaG1Db2RlJ1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGxhYmVsPSdT4buRIGjDs2EgxJHGoW4nXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT0nZ3JpZCBncmlkLWNvbHMtWzE2MHB4LDFmcl0gaXRlbXMtY2VudGVyIGxnOmdyaWQtY29scy1bMTIwcHgsMWZyXSdcclxuICAgICAgICAgICAgdHlwZT0ndGV4dCdcclxuICAgICAgICAgICAgbmFtZT0ncmVjaXBlTnVtYmVyJ1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGxhYmVsPSdLw70gaGnhu4d1J1xyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlciBsZzpncmlkLWNvbHMtWzEyMHB4LDFmcl0nXHJcbiAgICAgICAgICAgIHR5cGU9J3RleHQnXHJcbiAgICAgICAgICAgIG5hbWU9J2t5X2hpZXUnXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtmb3JtTW9kZSA9PT0gJ3ZpZXcnfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGb3JtRmllbGRcclxuICAgICAgICAgICAgbGFiZWw9J05nw6B5IGjDs2EgxJHGoW4nXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT0nZ3JpZCBncmlkLWNvbHMtWzE2MHB4LDFmcl0gaXRlbXMtY2VudGVyIGxnOmdyaWQtY29scy1bMTIwcHgsMWZyXSdcclxuICAgICAgICAgICAgdHlwZT0nZGF0ZSdcclxuICAgICAgICAgICAgbmFtZT0nbmhkJ1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEgaXRlbXMtY2VudGVyIGdhcC14LTQgbGc6Z3JpZC1jb2xzLTInPlxyXG4gICAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPSdncmlkIHctMy80IGdyaWQtY29scy1bMTYwcHgsMWZyXSBpdGVtcy1jZW50ZXIgbGc6Z3JpZC1jb2xzLVs5MHB4LDFmcl0nXHJcbiAgICAgICAgICAgICAgaW5wdXRDbGFzc05hbWU9J3ctMy80J1xyXG4gICAgICAgICAgICAgIGxhYmVsPSdOZ2/huqFpIHThu4cnXHJcbiAgICAgICAgICAgICAgbmFtZT0nZm9yZWlnbkN1cnJlbmN5J1xyXG4gICAgICAgICAgICAgIHR5cGU9J3NlbGVjdCdcclxuICAgICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgICAgICBvcHRpb25zPXtbXHJcbiAgICAgICAgICAgICAgICB7IHZhbHVlOiAnVk5EJywgbGFiZWw6ICdWTkQnIH0sXHJcbiAgICAgICAgICAgICAgICB7IHZhbHVlOiAnVVNEJywgbGFiZWw6ICdVU0QnIH0sXHJcbiAgICAgICAgICAgICAgICB7IHZhbHVlOiAnRVVSJywgbGFiZWw6ICdFVVInIH0sXHJcbiAgICAgICAgICAgICAgICB7IHZhbHVlOiAnSlBZJywgbGFiZWw6ICdKUFknIH1cclxuICAgICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPSdmbGV4LWdyb3cgbGc6bWwtNCdcclxuICAgICAgICAgICAgICBpbnB1dENsYXNzTmFtZT0ndy1mdWxsJ1xyXG4gICAgICAgICAgICAgIG5hbWU9J2V4Y2hhbmdlUmF0ZSdcclxuICAgICAgICAgICAgICB0eXBlPSd0ZXh0J1xyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtmb3JtTW9kZSA9PT0gJ3ZpZXcnfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8Rm9ybUZpZWxkXHJcbiAgICAgICAgICAgIGxhYmVsPSdUcuG6oW5nIHRow6FpJ1xyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLVsxNjBweCwxZnJdIGl0ZW1zLWNlbnRlciBsZzpncmlkLWNvbHMtWzEyMHB4LDFmcl0nXHJcbiAgICAgICAgICAgIG5hbWU9J3N0YXR1cydcclxuICAgICAgICAgICAgdHlwZT0nc2VsZWN0J1xyXG4gICAgICAgICAgICBkaXNhYmxlZD17Zm9ybU1vZGUgPT09ICd2aWV3J31cclxuICAgICAgICAgICAgb3B0aW9ucz17W1xyXG4gICAgICAgICAgICAgIHsgdmFsdWU6ICdjcmVhdGUnLCBsYWJlbDogJ8SQw6MgZ2hpIHPhu5UnIH0sXHJcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJ3BlbmRpbmcnLCBsYWJlbDogJ0No4budIGR1eeG7h3QnIH0sXHJcbiAgICAgICAgICAgICAgeyB2YWx1ZTogJ2FwcHJvdmVkJywgbGFiZWw6ICdDaMawYSBnaGkgc+G7lScgfVxyXG4gICAgICAgICAgICBdfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGb3JtRmllbGRcclxuICAgICAgICAgICAgbGFiZWw9J0Thu68gbGnhu4d1IMSRxrDhu6NjIG5o4bqtbidcclxuICAgICAgICAgICAgY2xhc3NOYW1lPSdncmlkIGdyaWQtY29scy1bMjAwcHgsMWZyXSBpdGVtcy1jZW50ZXInXHJcbiAgICAgICAgICAgIG5hbWU9J3N0YXR1cydcclxuICAgICAgICAgICAgdHlwZT0nY2hlY2tib3gnXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtmb3JtTW9kZSA9PT0gJ3ZpZXcnfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7c2hvd1RheENvZGVQb3B1cEZvcm0gJiYgKFxyXG4gICAgICAgIDxUYXhDb2RlUG9wVXBGb3JtXHJcbiAgICAgICAgICBzaG93VGF4Q29kZVBvcHVwRm9ybT17c2hvd1RheENvZGVQb3B1cEZvcm19XHJcbiAgICAgICAgICBzZXRTaG93VGF4Q29kZVBvcHVwRm9ybT17c2V0U2hvd1RheENvZGVQb3B1cEZvcm19XHJcbiAgICAgICAgICBmb3JtTW9kZT17Zm9ybU1vZGV9XHJcbiAgICAgICAgICBjdXJyZW50T2JqPXt7fX1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ29udGV4dCIsInVzZVdhdGNoIiwiTWFOQ0NTZWFyY2hDb2xCYXNpY0luZm8iLCJNYU5WU2VhcmNoQ29sQmFzaWNJbmZvIiwiVEtDb1NlYXJjaENvbEJhc2ljSW5mbyIsIk1hQ1RTZWFyY2hDb2xCYXNpY0luZm8iLCJCYXNpY0luZm9UYWJUeXBlMSIsIkJhc2ljSW5mb1RhYlR5cGUyIiwiVGF4Q29kZVBvcFVwRm9ybSIsIlBheW1lbnRUZXJtU2VsZWN0RmllbGQiLCJUeXBlMVRhYnMiLCJUeXBlMlRhYnMiLCJSYWRpb0J1dHRvbiIsIkFyaXRvRm9ybUNvbnRleHQiLCJGb3JtRmllbGQiLCJBcml0b0ljb24iLCJCYXNpY0luZm9UYWIiLCJmb3JtTW9kZSIsInNldFNob3dUYXhDb2RlUG9wdXBGb3JtIiwic2hvd1RheENvZGVQb3B1cEZvcm0iLCJmb3JtVHlwZSIsInNldEZvcm1UeXBlIiwiY29udHJvbCIsImlzUGF5bWVudENoZWNrZWQiLCJuYW1lIiwiZGVmYXVsdFZhbHVlIiwiaXNEaXNjb3VudENoZWNrZWQiLCJkaXYiLCJjbGFzc05hbWUiLCJsYWJlbCIsIm9wdGlvbnMiLCJ2YWx1ZSIsIm9yaWVudGF0aW9uIiwib25DaGFuZ2UiLCJzdHlsZSIsIndpZHRoIiwidHlwZSIsImRpc2FibGVkIiwid2l0aFNlYXJjaCIsInNlYXJjaEVuZHBvaW50Iiwic2VhcmNoQ29sdW1ucyIsImFjdGlvbkJ1dHRvbnMiLCJoZWFkZXJGaWVsZHMiLCJ0YWJzIiwiaWNvbiIsIm9uQ2xpY2siLCJsYWJlbENsYXNzTmFtZSIsImlucHV0Q2xhc3NOYW1lIiwiY3VycmVudE9iaiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx\n"));

/***/ })

});