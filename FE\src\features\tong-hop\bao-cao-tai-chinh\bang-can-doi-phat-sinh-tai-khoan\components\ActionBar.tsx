import { <PERSON>er, Pin, <PERSON>, Copy, FileSearch, RefreshCcw } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { SearchFormValues } from '../schema';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onViewBookClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onPrintClick?: () => void;
  onEditPrintClick?: () => void;
  onExportClick?: () => void;
  searchParams?: SearchFormValues | null;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onViewBookClick,
  onFixedColumnsClick,
  onRefreshClick,
  onPrintClick,
  onEditPrintClick,
  onExportClick,
  searchParams
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Bảng cân đối phát sinh tài khoản</h1>
        {searchParams && (
          <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
            <div className='mr-2 size-2 rounded-full bg-red-500' />
            Từ ngày {searchParams.date_from.toLocaleDateString('vi-VN')} đến ngày{' '}
            {searchParams.date_to.toLocaleDateString('vi-VN')}
          </span>
        )}
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}
    {onViewBookClick && (
      <AritoActionButton title='Xem sổ chữ T' icon={FileSearch} onClick={onViewBookClick} disabled={isViewDisabled} />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={Pin} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Printer} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      title='In ấn'
      icon={Printer}
      items={[
        {
          title: 'Bảng cân đối phát sinh',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 0
        },
        {
          title: 'Bảng cân đối phát sinh (ngoại tệ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 0
        },
        {
          title: 'Bảng cân đối phát sinh (song ngữ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Bảng cân đối phát sinh (ngoại tệ, song ngữ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Bảng cân đối phát sinh',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 2
        },
        {
          title: 'Bảng cân đối phát sinh (ngoại tệ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 2
        },
        {
          title: 'Bảng cân đối phát sinh (song ngữ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 3
        },
        {
          title: 'Bảng cân đối phát sinh (ngoại tệ, song ngữ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 3
        }
      ]}
    />

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintClick
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: onExportClick
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
