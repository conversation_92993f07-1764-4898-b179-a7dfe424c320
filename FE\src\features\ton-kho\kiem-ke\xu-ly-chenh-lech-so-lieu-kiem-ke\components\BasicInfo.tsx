import React from 'react';
import {
  accountColumns,
  getPartnerColumns,
  KhoHangSearchColBasicInfo,
  volSearchColumns,
  yeuCauKiemKeColumns
} from '../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo = ({ formMode }: { formMode?: 'add' | 'edit' | 'view' }) => (
  <div className='space-y-2 p-4'>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Ngày kiểm kê</Label>
        <div>
          <FormField name='ngay_kiem_ke' type='date' className='w-[11.25rem]' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Tk hàng thừa chờ giải quyết</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={accountColumns}
          dialogTitle='Danh mục tài khoản'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Tk hàng thiếu chờ xử lý</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={accountColumns}
          dialogTitle='Danh mục tài khoản'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Mã đối tượng</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={getPartnerColumns}
          dialogTitle='Danh mục đối tượng'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Mã kho</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={KhoHangSearchColBasicInfo}
          dialogTitle='Danh mục kho hàng'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Đơn vị</Label>
        <div>
          <FormField
            name='don_vi'
            type='select'
            className='w-[11.25rem]'
            disabled={formMode === 'view'}
            options={[{ value: 0, label: 'CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }]}
          />
        </div>
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Quyển phiếu nhập kho</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={volSearchColumns}
          dialogTitle='Danh mục quyển'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Quyển phiếu xuất kho</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={volSearchColumns}
          dialogTitle='Danh mục quyển'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Số phiếu kiểm kê</Label>
        <SearchField
          type='text'
          displayRelatedField={'rgname'}
          columnDisplay={'rg_code'}
          className='w-[11.25rem]'
          searchEndpoint='/regions/'
          searchColumns={yeuCauKiemKeColumns}
          dialogTitle='Danh mục quyển'
        />
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Ngày xử lý</Label>
        <div>
          <FormField name='ngay_xu_ly' type='date' className='w-[11.25rem]' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
    <div className='flex flex-col space-y-1'>
      <div className='flex items-center'>
        <Label className='w-52 min-w-52'>Xử lý</Label>
        <div>
          <FormField
            name='xu_ly'
            type='select'
            className='w-[11.25rem]'
            disabled={formMode === 'view'}
            options={[
              { value: 0, label: 'Xử lý chênh lệch' },
              { value: 1, label: 'Xóa kết quả xử lý chênh lệch' }
            ]}
          />
        </div>
      </div>
    </div>
  </div>
);

export default BasicInfo;
