import { GridColDef } from '@mui/x-data-grid';
import { <PERSON><PERSON> } from '@mui/material';
import { useState } from 'react';
import { z } from 'zod';
import { accountSearchColumns, khachHangSearchColumns, donViSearchColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { TaiK<PERSON>an, KhachHang, DonViChiNhanh } from '@/types/schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

// Define the schema for the search form
const searchSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  voucherNumberStart: z.string().optional(),
  voucherNumberEnd: z.string().optional(),
  voucherType: z.string().optional(),
  debitAccount: z.string().optional(),
  description: z.string().optional(),
  customerCode: z.string().optional(),
  creditAccount: z.string().optional(),
  unit: z.string().optional(),
  status: z.string().optional(),
  sortBy: z.string().optional()
});

type SearchFormValues = z.infer<typeof searchSchema>;

// Search field states hook for SearchDialog
const useSearchDialogFieldStates = () => {
  const [debitAccount, setDebitAccount] = useState<TaiKhoan | null>(null);
  const [creditAccount, setCreditAccount] = useState<TaiKhoan | null>(null);
  const [customer, setCustomer] = useState<KhachHang | null>(null);
  const [unit, setUnit] = useState<DonViChiNhanh | null>(null);

  return {
    debitAccount,
    setDebitAccount,
    creditAccount,
    setCreditAccount,
    customer,
    setCustomer,
    unit,
    setUnit
  };
};

export const SearchDialog = ({ open, onClose, onSearch }: SearchDialogProps) => {
  const searchFieldStates = useSearchDialogFieldStates();

  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc phiếu thu tiền'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      actions={
        <>
          <Button
            type='submit'
            form='search-form'
            variant='contained'
            sx={{
              borderRadius: 0,
              backgroundColor: '#1976d2',
              '&:hover': { backgroundColor: '#1565c0' }
            }}
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button
            onClick={onClose}
            variant='outlined'
            sx={{
              borderRadius: 0,
              borderColor: '#d32f2f',
              color: '#d32f2f',
              '&:hover': {
                borderColor: '#c62828',
                backgroundColor: 'rgba(211, 47, 47, 0.04)'
              }
            }}
          >
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <div className='space-y-2'>
              <div className='p-3'>
                <div className='flex items-center'>
                  <Label className='mt-3 flex items-center pr-2 text-left text-[13px] text-sm font-normal sm:mb-0'>
                    Ngày c/từ (từ/đến)
                  </Label>
                  <div className='flex flex-1 gap-2'>
                    <FormField name='startDate' type='date' className='w-full' />
                    <FormField name='endDate' type='date' className='w-full' />
                  </div>
                </div>

                <div className='flex items-center'>
                  <Label className='mt-3 flex w-[120px] items-center pr-2 text-left text-[13px] text-sm font-normal sm:mb-0'>
                    Số c/từ (từ/đến)
                  </Label>
                  <div className='flex flex-1 gap-2'>
                    <FormField name='so_ct' type='text' className='w-full' />
                    <FormField name='so_ct0' type='text' className='w-full' />
                  </div>
                </div>
                <div className='flex items-center'>
                  <Label className='w-[120px] text-sm font-medium'>Loại xuất</Label>
                  <FormField
                    name='ma_ngv'
                    type='select'
                    options={[
                      { value: 'Tất cả', label: 'Tất cả' },
                      { value: 'Thu theo hóa đơn', label: '1. Thu theo hóa đơn' },
                      {
                        value: 'Thu theo đối tượng',
                        label: '2. Thu theo đối tượng'
                      },
                      { value: 'Thu khác', label: '3. Thu khác' },
                      { value: 'Rút về nhập quỹ', label: '4. Rút về nhập quỹ' },
                      {
                        value: 'Bán hàng thu tiền ngày',
                        label: '5. Bán hàng thu tiền ngày'
                      }
                    ]}
                    className='w-full'
                  />
                </div>
                <div className='flex items-center'>
                  <Label className='w-[120px] text-sm font-medium'>Tài khoản nợ</Label>
                  <SearchField<TaiKhoan>
                    type='text'
                    dialogTitle='Danh mục tài khoản'
                    searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                    searchColumns={accountSearchColumns}
                    displayRelatedField='name'
                    columnDisplay='code'
                    value={searchFieldStates.debitAccount?.code || ''}
                    relatedFieldValue={searchFieldStates.debitAccount?.name || ''}
                    onRowSelection={searchFieldStates.setDebitAccount}
                    className='w-full'
                  />
                </div>

                <div className='flex items-center'>
                  <Label className='w-[120px] text-sm font-medium'>Diễn giải</Label>
                  <FormField name='dien_giai' type='text' className='w-full' />
                </div>
              </div>

              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'customer',
                    label: 'Chi tiết',
                    component: (
                      <div className='p-3'>
                        <div className='flex items-center'>
                          <Label className='w-[120px] text-sm font-medium'>Mã khách hàng</Label>
                          <SearchField<KhachHang>
                            type='text'
                            dialogTitle='Danh mục khách hàng'
                            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                            searchColumns={khachHangSearchColumns}
                            displayRelatedField='customer_name'
                            columnDisplay='customer_code'
                            value={searchFieldStates.customer?.customer_code || ''}
                            relatedFieldValue={searchFieldStates.customer?.customer_name || ''}
                            onRowSelection={searchFieldStates.setCustomer}
                            className='w-full'
                          />
                        </div>

                        <div className='flex items-center'>
                          <Label className='w-[120px] text-sm font-medium'>Tài khoản có</Label>
                          <SearchField<TaiKhoan>
                            type='text'
                            dialogTitle='Danh mục tài khoản'
                            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                            searchColumns={accountSearchColumns}
                            displayRelatedField='name'
                            columnDisplay='code'
                            value={searchFieldStates.creditAccount?.code || ''}
                            relatedFieldValue={searchFieldStates.creditAccount?.name || ''}
                            onRowSelection={searchFieldStates.setCreditAccount}
                            className='w-full'
                          />
                        </div>
                      </div>
                    )
                  }
                ]}
              />

              <div className='border-t border-t-gray-200 p-3'>
                <div className='flex items-center'>
                  <Label className='w-[120px] text-sm font-medium'>Đơn vị</Label>
                  <SearchField<DonViChiNhanh>
                    type='text'
                    dialogTitle='Danh mục đơn vị'
                    searchEndpoint={`/${QUERY_KEYS.DON_VI_CHI_NHANH}`}
                    searchColumns={donViSearchColumns}
                    displayRelatedField='ten_unit'
                    columnDisplay='ma_unit'
                    value={searchFieldStates.unit?.ma_unit || ''}
                    relatedFieldValue={searchFieldStates.unit?.ten_unit || ''}
                    onRowSelection={searchFieldStates.setUnit}
                    className='w-full'
                  />
                </div>
                <div className='flex items-center'>
                  <Label className='w-[120px] text-sm font-medium'>Trạng thái</Label>
                  <FormField
                    name='status'
                    type='select'
                    options={[
                      { value: 'Tất cả', label: 'Tất cả' },
                      { value: 'Chưa ghi sổ', label: 'Chưa ghi sổ' },
                      { value: 'Chờ duyệt', label: 'Chờ duyệt' },
                      { value: 'Đã ghi sổ', label: 'Đã ghi sổ' },
                      { value: 'Đang thực hiện', label: 'Đang thực hiện' },
                      { value: 'Hoàn thành', label: 'Hoàn thành' },
                      { value: 'Nhập quỹ', label: 'Nhập quỹ' },
                      { value: 'Hủy', label: 'Hủy' }
                    ]}
                    className='w-full'
                  />
                </div>
                <div className='flex items-center'>
                  <Label className='w-[120px] text-sm font-medium'>Lọc theo người sd</Label>
                  <FormField
                    name='sortBy'
                    type='select'
                    options={[
                      { value: 'Tất cả', label: 'Tất cả' },
                      {
                        value: 'Lọc theo người tạo',
                        label: 'Lọc theo người tạo'
                      }
                    ]}
                    className='w-full'
                  />
                </div>
              </div>
            </div>
          </div>
        }
      />
    </AritoDialog>
  );
};
