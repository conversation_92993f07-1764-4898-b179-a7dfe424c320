import { Search, RefreshCw, FileText, Table, Printer } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onSearchClick: () => void;
  onEditPrintTemplateClick: () => void;
  onRefreshClick: () => void;
  onFixedColumnsClick: () => void;
  onExportDataClick: () => void;
  searchParams?: any;
}

const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onEditPrintTemplateClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  searchParams
}) => {
  console.log(searchParams);
  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'><PERSON><PERSON><PERSON> c<PERSON><PERSON> lưu chuyển tiền tệ trực tiếp</h1>
          <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
            <div className='mr-2 size-2 rounded-full bg-red-500' />
            Từ ngày {(searchParams?.ngay_ct11 as Date).toLocaleDateString('vi-VN')} đến ngày{' '}
            {(searchParams?.ngay_ct12 as Date).toLocaleDateString('vi-VN')}{' '}
          </span>
        </div>
      }
    >
      <AritoActionButton icon={Search} onClick={onSearchClick} title='Tìm kiếm' variant='primary' />
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Báo cáo lưu chuyển tiền tệ',
            icon: <AritoIcon icon={569} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ clicked');
            },
            group: 0
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ giữa niên độ',
            icon: <AritoIcon icon={569} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ giữa niên độ clicked');
            },
            group: 0
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ (song ngữ)',
            icon: <AritoIcon icon={569} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ (song ngữ) clicked');
            },
            group: 1
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ giữa niên độ (song ngữ)',
            icon: <AritoIcon icon={569} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ giữa niên độ (song ngữ) clicked');
            },
            group: 1
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ',
            icon: <AritoIcon icon={18} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ (Excel) clicked');
            },
            group: 2
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ giữa niên độ',
            icon: <AritoIcon icon={18} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ giữa niên độ (Excel) clicked');
            },
            group: 2
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ (song ngữ)',
            icon: <AritoIcon icon={18} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ (song ngữ) (Excel) clicked');
            },
            group: 3
          },
          {
            title: 'Báo cáo lưu chuyển tiền tệ giữa niên độ (song ngữ)',
            icon: <AritoIcon icon={18} />,
            onClick: () => {
              console.log('Báo cáo lưu chuyển tiền tệ giữa niên độ (song ngữ) (Excel) clicked');
            },
            group: 3
          }
        ]}
      />
      <AritoActionButton icon={RefreshCw} onClick={onRefreshClick} title='Refresh' />
      <AritoActionButton icon={Table} onClick={onFixedColumnsClick} title='Cố định cột' />
      <AritoActionButton icon={FileText} onClick={onExportDataClick} title='Kết xuất dữ liệu' />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            icon: <AritoIcon icon={864} />,
            title: 'Chỉnh sửa mẫu in',
            onClick: () => {
              console.log('Chỉnh sửa mẫu in clicked');
              onEditPrintTemplateClick();
            }
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
