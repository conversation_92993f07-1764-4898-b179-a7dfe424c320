import { useState, useEffect } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { capNhatKiemKeSchema } from '../schemas';
import ConfirmDialog from './ConfirmDialog';
import BasicInfoTab from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: any;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  formMode,
  initialData
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);

  const handleSubmit = (data: any) => {
    onSubmit(data);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={`${formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Cập nhật kiểm kê'}`}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={capNhatKiemKeSchema}
          onSubmit={handleSubmit}
          initialData={initialData || {}}
          className='w-[800px]'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <BasicInfoTab formMode={formMode} />
            </div>
          }
          bottomBar={
            <>
              {formMode === 'view' && (
                <BottomBar
                  mode={formMode}
                  onSubmit={() => {}}
                  onClose={handleClose}
                  onAdd={onAdd}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onCopy={onCopy}
                />
              )}
              {formMode !== 'view' && <BottomBar mode={formMode} onSubmit={() => {}} onClose={handleClose} />}
            </>
          }
          classNameBottomBar='relative'
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
