"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx":
/*!*************************************************************************************!*\
  !*** ./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var _constants___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst BasicInfo = (param)=>{\n    let { formMode, searchFields } = param;\n    _s();\n    const { vatTu, setVatTu, donViTinh, setDonViTinh, nhaCungCap, setNhaCungCap, ngoaiTe, setNgoaiTe } = searchFields;\n    const isViewMode = formMode === \"view\";\n    const { setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFormContext)();\n    const giaoDict = watch(\"giao_dich\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-h-[calc(100vh-150px)] overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 md:p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-y-4 md:gap-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 md:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Giao dịch\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                style: {\n                                                    minWidth: \"200px\",\n                                                    marginRight: \"40px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"giao_dich_service\",\n                                                        name: \"giao_dich\",\n                                                        value: \"service\",\n                                                        defaultChecked: giaoDict === \"service\" || !giaoDict,\n                                                        onChange: (e)=>setValue(\"giao_dich\", e.target.value),\n                                                        disabled: isViewMode,\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"giao_dich_service\",\n                                                        className: \"cursor-pointer text-sm whitespace-nowrap\",\n                                                        children: \"DV. Mua dịch vụ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                style: {\n                                                    minWidth: \"280px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        id: \"giao_dich_asset\",\n                                                        name: \"giao_dich\",\n                                                        value: \"asset\",\n                                                        defaultChecked: giaoDict === \"asset\",\n                                                        onChange: (e)=>setValue(\"giao_dich\", e.target.value),\n                                                        disabled: isViewMode,\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"giao_dich_asset\",\n                                                        className: \"cursor-pointer text-sm whitespace-nowrap\",\n                                                        children: \"TS. Mua t\\xe0i sản/c\\xf4ng cụ chi tiết\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"M\\xe3 vật tư\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_vt\",\n                                    columnDisplay: \"ma_vt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.VAT_TU, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.vatTuSearchColumns,\n                                    dialogTitle: \"Danh mục vật tư\",\n                                    value: (vatTu === null || vatTu === void 0 ? void 0 : vatTu.ma_vt) || \"\",\n                                    onRowSelection: setVatTu,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Đơn vị t\\xednh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_dvt\",\n                                    columnDisplay: \"dvt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.DON_VI_TINH, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.dvtSearchColumns,\n                                    dialogTitle: \"Danh mục đơn vị t\\xednh\",\n                                    value: (donViTinh === null || donViTinh === void 0 ? void 0 : donViTinh.dvt) || \"\",\n                                    onRowSelection: setDonViTinh,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ng\\xe0y hiệu lực\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"date\",\n                                    name: \"ngay_hieu_luc\",\n                                    className: \"w-40\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Nh\\xe0 cung cấp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"customer_name\",\n                                    columnDisplay: \"customer_code\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NHA_CUNG_CAP, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.khachHangSearchColumns,\n                                    dialogTitle: \"Danh mục nh\\xe0 cung cấp\",\n                                    value: (nhaCungCap === null || nhaCungCap === void 0 ? void 0 : nhaCungCap.customer_code) || \"\",\n                                    onRowSelection: setNhaCungCap,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ngoại tệ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_nt\",\n                                    columnDisplay: \"ma_nt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NGOAI_TE, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.ngoaiTeSearchColumns,\n                                    dialogTitle: \"Danh mục ngoại tệ\",\n                                    value: (ngoaiTe === null || ngoaiTe === void 0 ? void 0 : ngoaiTe.ma_nt) || \"\",\n                                    onRowSelection: setNgoaiTe,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Số lượng từ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"so_luong_tu\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Gi\\xe1 mua\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"gia_mua\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"trang_thai\",\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Trạng th\\xe1i\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    id: \"trang_thai\",\n                                    type: \"select\",\n                                    name: \"trang_thai\",\n                                    options: [\n                                        {\n                                            label: \"1. C\\xf2n sử dụng\",\n                                            value: 1\n                                        },\n                                        {\n                                            label: \"0. Kh\\xf4ng sử dụng\",\n                                            value: 0\n                                        }\n                                    ],\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfo, \"8gSdUFvM7fwO6r+A8M34gxoVIPM=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFormContext\n    ];\n});\n_c = BasicInfo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BasicInfo);\nvar _c;\n$RefreshReg$(_c, \"BasicInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx\n"));

/***/ })

});