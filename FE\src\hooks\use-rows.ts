import { useState } from 'react';

/**
 * Hook for managing row selection state
 * @template T The type of the selected object
 */
export const useRows = <T = any>() => {
  const [selectedObj, setSelectedObj] = useState<T | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: { id: string; row: T }) => {
    setSelectedRowIndex(params.id);
    setSelectedObj(params.row);
  };

  const clearSelection = () => {
    setSelectedRowIndex(null);
    setSelectedObj(null);
  };

  return {
    selectedObj,
    setSelectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};
