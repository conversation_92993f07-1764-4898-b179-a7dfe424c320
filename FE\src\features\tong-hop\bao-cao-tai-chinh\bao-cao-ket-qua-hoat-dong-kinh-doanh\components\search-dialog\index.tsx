import { But<PERSON> } from '@mui/material';
import React from 'react';
import { SearchFormValues, SearchFormSchema, initialSearchValues } from '../../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { BasicInfoTab } from './BasicInfoTab';
import { DetailTab } from './DetailTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: any) => void;

  openSaveTemplateDialog: boolean;
  onOpenSaveTemplateDialog: () => void;
  onCloseSaveTemplateDialog: () => void;
  onSaveTemplate: (data: any) => void;
}

const SearchDialog = ({
  openSearchDialog,
  onCloseSearchDialog,
  onSearch,
  openSaveTemplateDialog,
  onOpenSaveTemplateDialog,
  onCloseSaveTemplateDialog,
  onSaveTemplate
}: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Báo cáo kết quả hoạt động kinh doanh'
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={SearchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail_tab',
                  label: 'Chi tiết',
                  component: (
                    <DetailTab
                      formMode='add'
                      open={openSaveTemplateDialog}
                      onOpen={onOpenSaveTemplateDialog}
                      onClose={onCloseSaveTemplateDialog}
                      onSave={onSaveTemplate}
                    />
                  )
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
            <Button onClick={onCloseSearchDialog} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
