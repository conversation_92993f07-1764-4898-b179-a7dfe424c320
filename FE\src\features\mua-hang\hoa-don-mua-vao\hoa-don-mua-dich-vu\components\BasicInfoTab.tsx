import { useState, useContext } from 'react';
import { useWatch } from 'react-hook-form';
import {
  MaNCCSearchColBasicInfo,
  MaNVSearchColBasicInfo,
  TKCoSearchColBasicInfo,
  MaCTSearchColBasicInfo
} from '../cols-definition';
import BasicInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1';
import BasicInfoTabType2 from '@/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2';
import TaxCodePopUpForm from '@/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm';
import { PaymentTermSelectField } from '@/components/custom/arito/form/search-fields';
import { Type1Tabs } from '@/components/cac-loai-form/popup-form-type-1/Tabs';
import { Type2Tabs } from '@/components/cac-loai-form/popup-form-type-2/Tabs';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { AritoFormContext } from '@/components/custom/arito/form/form';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoIcon } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
interface Props {
  formMode: 'add' | 'edit' | 'view';
  showTaxCodePopupForm: boolean;
  setShowTaxCodePopupForm: (value: boolean) => void;
}
export const BasicInfoTab = ({ formMode, setShowTaxCodePopupForm, showTaxCodePopupForm }: Props) => {
  const [formType, setFormType] = useState<'chung_tu' | 'hoa_don' | 'phieu_nhap'>('chung_tu');
  const { control } = useContext(AritoFormContext);

  const isPaymentChecked = useWatch({
    control,
    name: 'payment',
    defaultValue: false
  });

  const isDiscountChecked = useWatch({
    control,
    name: 'discount',
    defaultValue: false
  });

  return (
    <div className='flex p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
        {/* Cột trái */}
        <div className='col-span-3 space-y-2'>
          <div className='grid grid-cols-1 space-x-2 lg:grid-cols-2'>
            <div className='grid grid-cols-[120px,1fr] items-center'>
              <label className='text-sm'>Giao dịch</label>
              <div className='flex items-center' style={{ gap: '32px' }}>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='dataType_service'
                    name='dataType'
                    value='service'
                    defaultChecked={true}
                    className='h-4 w-4'
                  />
                  <label htmlFor='dataType_service' className='text-sm cursor-pointer'>
                    DV. Mua dịch vụ
                  </label>
                </div>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='dataType_asset'
                    name='dataType'
                    value='asset'
                    className='h-4 w-4'
                  />
                  <label htmlFor='dataType_asset' className='text-sm cursor-pointer'>
                    TS. Mua tài sản/công cụ chi tiết
                  </label>
                </div>
              </div>
            </div>
            <div className='flex flex-col'>
              <div className='flex flex-row'>
                <div className='flex items-center'>
                  <div className='flex items-center' style={{ width: '160px' }}>
                    <FormField className='mr-2' type='checkbox' name='payment' disabled={formMode === 'view'} />
                    {!isPaymentChecked && <label className='text-sm font-medium'>Chi tiền</label>}
                  </div>

                  {isPaymentChecked && (
                    <FormField
                      type='select'
                      name='paymentMethod'
                      className='-ml-16 -mt-2 flex w-28 items-center'
                      options={[
                        { value: 'cash', label: 'Tiền mặt' },
                        { value: 'bank', label: 'Chuyển khoản' },
                        { value: 'credit', label: 'Khác' }
                      ]}
                      disabled={formMode === 'view'}
                    />
                  )}
                </div>

                <div className='flex items-center'>
                  <div className='flex items-center' style={{ width: '160px' }}>
                    <FormField className='-ml-6' type='checkbox' name='discount' disabled={formMode === 'view'} />
                    <label className='text-sm font-medium'>Chiết khấu</label>
                  </div>

                  {isDiscountChecked && (
                    <div className='-ml-16 -mt-3 flex items-center space-x-2'>
                      <FormField
                        type='select'
                        name='discountType'
                        className='w-48'
                        options={[
                          { value: 'byHand', label: 'Tự nhập' },
                          { value: 'percent', label: 'Giảm % theo hóa đơn' },
                          {
                            value: 'fixed',
                            label: 'Giảm tiền trên tổng hóa đơn'
                          }
                        ]}
                        disabled={formMode === 'view'}
                      />
                      <FormField type='text' name='discountValue' className='w-12' disabled={formMode === 'view'} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
            <FormField
              className='grid grid-cols-[160px,1fr] items-center'
              type='text'
              label='Mã nhà cung cấp'
              name='supplierCode'
              disabled={formMode === 'view'}
              withSearch={true}
              searchEndpoint='accounting/supplier'
              searchColumns={MaNCCSearchColBasicInfo}
              actionButtons={['add', 'edit']}
              headerFields={<BasicInfoTabType1 formMode={formMode} />}
              tabs={Type1Tabs({ formMode })}
            />
            <div className='flex items-center gap-x-2'>
              <FormField
                className='grid grid-cols-[160px,1fr] items-center'
                type='text'
                label='Mã số thuế'
                name='taxCode'
                disabled={formMode === 'view'}
              />
              <div className='cursor-pointer'>
                <AritoIcon icon={15} />
              </div>
              <div className='cursor-pointer' onClick={() => setShowTaxCodePopupForm(true)}>
                <AritoIcon icon={58} />
              </div>
            </div>
          </div>
          <FormField
            className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[160px,1fr] sm:items-center'
            type='text'
            label='Tên nhà cung cấp'
            name='supplierName'
            disabled={formMode === 'view'}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            label='Địa chỉ'
            name='address'
            type='text'
            disabled={formMode === 'view'}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            label='Mã nhân viên'
            name='employeeCode'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchColumns={MaNVSearchColBasicInfo}
            searchEndpoint='hr/employee'
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            label='Tài khoản có'
            name='accountNumber'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='accounting/account'
            searchColumns={TKCoSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
          <FormField
            className='grid grid-cols-[160px,1fr] items-center'
            label='Diễn giải'
            name='description'
            type='text'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Cột giữa */}
        <div className='col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center text-red-600 lg:grid-cols-[120px,1fr]'
            label='Dư công nợ'
            type='number'
            name='debt'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Người giao hàng'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            type='text'
            name='deliveryPerson'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Email'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            name='email'
            type='text'
            disabled={formMode === 'view'}
          />
          <PaymentTermSelectField
            name='paymentTerm'
            label='Hạn thanh toán'
            formMode={formMode === 'view' ? 'view' : formMode === 'edit' ? 'edit' : 'add'}
            labelClassName='w-32'
            inputClassName='w-48'
          />
        </div>

        <div className='col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1'>
          <FormField
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            label='Số chứng từ'
            withSearch={true}
            type='text'
            name='bookNumber'
            disabled={formMode === 'view'}
            searchEndpoint='accounting/transaction'
            searchColumns={MaCTSearchColBasicInfo}
          />
          <FormField
            label='Ngày chứng từ'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            type='date'
            name='dhmCode'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Số hóa đơn'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            type='text'
            name='recipeNumber'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Ký hiệu'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            type='text'
            name='ky_hieu'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Ngày hóa đơn'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            type='date'
            name='nhd'
            disabled={formMode === 'view'}
          />

          <div className='grid grid-cols-1 items-center gap-x-4 lg:grid-cols-2'>
            <FormField
              className='grid w-3/4 grid-cols-[160px,1fr] items-center lg:grid-cols-[90px,1fr]'
              inputClassName='w-3/4'
              label='Ngoại tệ'
              name='foreignCurrency'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: 'VND', label: 'VND' },
                { value: 'USD', label: 'USD' },
                { value: 'EUR', label: 'EUR' },
                { value: 'JPY', label: 'JPY' }
              ]}
            />
            <FormField
              className='flex-grow lg:ml-4'
              inputClassName='w-full'
              name='exchangeRate'
              type='text'
              disabled={formMode === 'view'}
            />
          </div>
          <FormField
            label='Trạng thái'
            className='grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]'
            name='status'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { value: 'create', label: 'Đã ghi sổ' },
              { value: 'pending', label: 'Chờ duyệt' },
              { value: 'approved', label: 'Chưa ghi sổ' }
            ]}
          />
          <FormField
            label='Dữ liệu được nhận'
            className='grid grid-cols-[200px,1fr] items-center'
            name='status'
            type='checkbox'
            disabled={formMode === 'view'}
          />
        </div>
      </div>

      {showTaxCodePopupForm && (
        <TaxCodePopUpForm
          showTaxCodePopupForm={showTaxCodePopupForm}
          setShowTaxCodePopupForm={setShowTaxCodePopupForm}
          formMode={formMode}
          currentObj={{}}
        />
      )}
    </div>
  );
};
