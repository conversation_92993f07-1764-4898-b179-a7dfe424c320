import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { ObjectFilterTab } from './filter-tabs/ObjectFilterTab';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { generalJournalFilterSchema } from '../schemas';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { DetailTab } from './filter-tabs/DetailTab';
import { MainTab } from './filter-tabs/MainTab';
import { MoreTab } from './filter-tabs/MoreTab';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

const SearchDialog = ({ open, onClose, onSubmit }: InitialSearchDialogProps) => {
  const handleSubmit = (data: any) => {
    onSubmit(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='<PERSON><PERSON><PERSON> kê chứng từ theo đối tượng'
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        onSubmit={handleSubmit}
        schema={generalJournalFilterSchema}
        headerFields={
          <div className='flex flex-row gap-2'>
            <MainTab />
          </div>
        }
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab />
          },
          {
            id: 'object',
            label: 'Lọc theo đối tượng',
            component: <ObjectFilterTab />
          },
          {
            id: 'more',
            label: 'Khác',
            component: <MoreTab />
          }
        ]}
        className='w-full md:min-w-[700px] lg:min-w-[800px]'
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='add' onSubmit={() => {}} onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
