import { Eye, FileText, Pencil, Plus, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  isEditDisabled?: boolean;
  onViewClick: () => void;
  isViewDisabled?: boolean;
  onDeleteClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  isEditDisabled = true,
  onViewClick,
  isViewDisabled = true,
  onDeleteClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON> báo bút toán kết chuyển</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton title='Xoá' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} disabled={isViewDisabled} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
