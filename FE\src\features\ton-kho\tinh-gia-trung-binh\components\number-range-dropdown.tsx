import { useFormContext } from 'react-hook-form';
import React from 'react';
import dayjs from 'dayjs';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { DATE_RANGE_OPTIONS } from '@/lib/dateUtil';

interface NumberRangeDropdownProps {
  fromName: string;
  toName: string;
}

const getQuarterMonths = (quarter: number) => ({
  from: (quarter - 1) * 3 + 1,
  to: quarter * 3
});

const NumberRangeDropdown: React.FC<NumberRangeDropdownProps> = ({ fromName, toName }) => {
  const { setValue } = useFormContext();

  const handleSelectOption = (optionId: string) => {
    const currentMonth = dayjs().month() + 1;
    const currentQuarter = Math.ceil(currentMonth / 3);

    const monthRanges = {
      thisMonth: { from: currentMonth, to: currentMonth },
      lastMonth: { from: currentMonth === 1 ? 12 : currentMonth - 1, to: currentMonth === 1 ? 12 : currentMonth - 1 },
      nextMonth: { from: currentMonth === 12 ? 1 : currentMonth + 1, to: currentMonth === 12 ? 1 : currentMonth + 1 },
      quarter1: getQuarterMonths(1),
      quarter2: getQuarterMonths(2),
      quarter3: getQuarterMonths(3),
      quarter4: getQuarterMonths(4),
      thisQuarter: getQuarterMonths(currentQuarter),
      lastQuarter: getQuarterMonths(currentQuarter === 1 ? 4 : currentQuarter - 1)
    };

    // Handle specific months (month1-month12)
    const monthMatch = optionId.match(/^month(\d{1,2})$/);
    if (monthMatch) {
      const month = parseInt(monthMatch[1], 10);
      setValue(fromName, month);
      setValue(toName, month);
      return;
    }

    // Handle predefined ranges
    const range = monthRanges[optionId as keyof typeof monthRanges];
    if (range) {
      setValue(fromName, range.from);
      setValue(toName, range.to);
    }
  };

  const items = DATE_RANGE_OPTIONS.filter(option =>
    /^(thisMonth|lastMonth|nextMonth|month\d{1,2}|quarter[1-4]|thisQuarter|lastQuarter)$/.test(option.id)
  ).map(option => ({
    label: option.label,
    value: option.id,
    onClick: () => handleSelectOption(option.id)
  }));

  return (
    <div className='flex items-center gap-2'>
      <div className='grid flex-1 grid-cols-1 gap-4 md:grid-cols-2'>
        <FormField name={fromName} type='number' className='w-20 min-w-[100px]' />
        <FormField name={toName} type='number' className='w-20 min-w-[100px]' />
      </div>
      <RadixHoverDropdown items={items} iconNumber={284} />
    </div>
  );
};

export default NumberRangeDropdown;
