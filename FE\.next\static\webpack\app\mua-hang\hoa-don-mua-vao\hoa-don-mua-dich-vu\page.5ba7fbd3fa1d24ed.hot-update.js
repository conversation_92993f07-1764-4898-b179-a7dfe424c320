"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page",{

/***/ "(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BasicInfoTab: function() { return /* binding */ BasicInfoTab; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _cols_definition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cols-definition */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/cols-definition.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2.tsx\");\n/* harmony import */ var _components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm */ \"(app-pages-browser)/./src/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm.tsx\");\n/* harmony import */ var _components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom/arito/form/search-fields */ \"(app-pages-browser)/./src/components/custom/arito/form/search-fields/index.ts\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/Tabs.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/Tabs.tsx\");\n/* harmony import */ var _components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom/arito/form/radio-button */ \"(app-pages-browser)/./src/components/custom/arito/form/radio-button/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom/arito/form/form */ \"(app-pages-browser)/./src/components/custom/arito/form/form/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BasicInfoTab = (param)=>{\n    let { formMode, setShowTaxCodePopupForm, showTaxCodePopupForm } = param;\n    _s();\n    const [formType, setFormType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chung_tu\");\n    const { control } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__.AritoFormContext);\n    const isPaymentChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch)({\n        control,\n        name: \"payment\",\n        defaultValue: false\n    });\n    const isDiscountChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch)({\n        control,\n        name: \"discount\",\n        defaultValue: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-x-8 lg:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"w-32 text-sm font-medium\",\n                                        children: \"Giao dịch\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__.RadioButton, {\n                                            name: \"dataType\",\n                                            options: transactionTypeOptions,\n                                            defaultValue: \"service\",\n                                            orientation: \"horizontal\",\n                                            onChange: ()=>{},\n                                            className: \"gap-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-8 pl-36\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"checkbox\",\n                                                name: \"payment\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                                className: \"text-sm\",\n                                                children: \"Chi tiền\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"checkbox\",\n                                                name: \"discount\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                                className: \"text-sm\",\n                                                children: \"Chiết khấu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                type: \"text\",\n                                label: \"M\\xe3 nh\\xe0 cung cấp\",\n                                name: \"supplierCode\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/supplier\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNCCSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__.Type1Tabs)({\n                                    formMode\n                                }),\n                                placeholder: \"Nhập v\\xe0 tra cứu\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                type: \"text\",\n                                label: \"T\\xean nh\\xe0 cung cấp\",\n                                name: \"supplierName\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"T\\xean nh\\xe0 cung cấp/đơn vị\",\n                                defaultValue: \"T\\xean nh\\xe0 cung cấp/đơn vị\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Địa chỉ\",\n                                name: \"address\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"Địa chỉ nh\\xe0 cung cấp\",\n                                defaultValue: \"Địa chỉ nh\\xe0 cung cấp\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"M\\xe3 nh\\xe2n vi\\xean\",\n                                name: \"employeeCode\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNVSearchColBasicInfo,\n                                searchEndpoint: \"hr/employee\",\n                                placeholder: \"sdfsd\",\n                                defaultValue: \"DSFSDFSD\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"T\\xe0i khoản c\\xf3\",\n                                name: \"accountNumber\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/account\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.TKCoSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__.Type2Tabs)({\n                                    formMode\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Diễn giải\",\n                                name: \"description\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"M\\xe3 số thuế\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"text\",\n                                                name: \"taxCode\",\n                                                disabled: formMode === \"view\",\n                                                className: \"flex-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 15\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                onClick: ()=>setShowTaxCodePopupForm(true),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 58\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Dự c\\xf4ng nợ\",\n                                type: \"number\",\n                                name: \"debt\",\n                                disabled: formMode === \"view\",\n                                defaultValue: \"0\",\n                                inputClassName: \"text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Người giao h\\xe0ng\",\n                                type: \"text\",\n                                name: \"deliveryPerson\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"Nhập t\\xean người giao h\\xe0ng\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\",\n                                label: \"Email\",\n                                name: \"email\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                placeholder: \"Email\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__.PaymentTermSelectField, {\n                                name: \"paymentTerm\",\n                                label: \"Hạn thanh to\\xe1n\",\n                                formMode: formMode === \"view\" ? \"view\" : formMode === \"edit\" ? \"edit\" : \"add\",\n                                labelClassName: \"w-40\",\n                                inputClassName: \"flex-1\",\n                                className: \"grid grid-cols-[160px,1fr] items-center gap-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center text-red-600 lg:grid-cols-[120px,1fr]\",\n                                label: \"Dư c\\xf4ng nợ\",\n                                type: \"number\",\n                                name: \"debt\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Người giao h\\xe0ng\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"deliveryPerson\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Email\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"email\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__.PaymentTermSelectField, {\n                                name: \"paymentTerm\",\n                                label: \"Hạn thanh to\\xe1n\",\n                                formMode: formMode === \"view\" ? \"view\" : formMode === \"edit\" ? \"edit\" : \"add\",\n                                labelClassName: \"w-32\",\n                                inputClassName: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                label: \"Số chứng từ\",\n                                withSearch: true,\n                                type: \"text\",\n                                name: \"bookNumber\",\n                                disabled: formMode === \"view\",\n                                searchEndpoint: \"accounting/transaction\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaCTSearchColBasicInfo\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y chứng từ\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"dhmCode\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Số h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"recipeNumber\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"K\\xfd hiệu\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"ky_hieu\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"nhd\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 items-center gap-x-4 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid w-3/4 grid-cols-[160px,1fr] items-center lg:grid-cols-[90px,1fr]\",\n                                        inputClassName: \"w-3/4\",\n                                        label: \"Ngoại tệ\",\n                                        name: \"foreignCurrency\",\n                                        type: \"select\",\n                                        disabled: formMode === \"view\",\n                                        options: [\n                                            {\n                                                value: \"VND\",\n                                                label: \"VND\"\n                                            },\n                                            {\n                                                value: \"USD\",\n                                                label: \"USD\"\n                                            },\n                                            {\n                                                value: \"EUR\",\n                                                label: \"EUR\"\n                                            },\n                                            {\n                                                value: \"JPY\",\n                                                label: \"JPY\"\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"flex-grow lg:ml-4\",\n                                        inputClassName: \"w-full\",\n                                        name: \"exchangeRate\",\n                                        type: \"text\",\n                                        disabled: formMode === \"view\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Trạng th\\xe1i\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"status\",\n                                type: \"select\",\n                                disabled: formMode === \"view\",\n                                options: [\n                                    {\n                                        value: \"create\",\n                                        label: \"Đ\\xe3 ghi sổ\"\n                                    },\n                                    {\n                                        value: \"pending\",\n                                        label: \"Chờ duyệt\"\n                                    },\n                                    {\n                                        value: \"approved\",\n                                        label: \"Chưa ghi sổ\"\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Dữ liệu được nhận\",\n                                className: \"grid grid-cols-[200px,1fr] items-center\",\n                                name: \"status\",\n                                type: \"checkbox\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            showTaxCodePopupForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                showTaxCodePopupForm: showTaxCodePopupForm,\n                setShowTaxCodePopupForm: setShowTaxCodePopupForm,\n                formMode: formMode,\n                currentObj: {}\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfoTab, \"009JD+HxJuQpJs08QjxCCt52q+A=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch\n    ];\n});\n_c = BasicInfoTab;\nvar _c;\n$RefreshReg$(_c, \"BasicInfoTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx\n"));

/***/ })

});