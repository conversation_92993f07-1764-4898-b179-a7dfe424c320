import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const AddTab = ({ formMode }: { formMode?: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vật tư:</Label>
          <div>
            <FormField
              name='ma_vat_tu'
              type='text'
              className='w-[420px]'
              disabled={formMode === 'view'}
              withSearch={true}
            />
          </div>
        </div>
      </div>

      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đvt:</Label>
          <div>
            <FormField name='dvt' type='text' className='w-[420px]' disabled={formMode === 'view'} withSearch={true} />
          </div>
        </div>
      </div>

      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tồn kho:</Label>
          <div>
            <FormField name='ton_kho' type='number' className='w-[420px]' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>

      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Sl kiểm kê:</Label>
          <div>
            <FormField name='sl_kiem_ke' type='number' className='w-[420px]' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddTab;
