import React, { useState } from 'react';
import { Button } from '@mui/material';
import { searchSchema, initialValues, SearchFormValues } from '../schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { BasicInfo, DetailTab, OtherTab } from './tabs';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: SearchFormValues) => void;
}

const InitialSearchDialog = ({ open, onClose, onSearch }: InitialSearchDialogProps) => {
  const [formValues, setFormValues] = useState<SearchFormValues>(initialValues);

  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo kiểm kê'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailTab />
                },
                {
                  id: 'others',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2 py-1'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
