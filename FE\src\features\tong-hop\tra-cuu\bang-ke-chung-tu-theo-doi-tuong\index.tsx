'use client';

import { useState } from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { getDocumentsListColumns } from './cols-definition';
import { useDialogState, useRowSelection } from './hooks';
import { ActionBar, SearchDialog } from './components';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { generalJournalSchema } from './schemas';

export default function DocumentsListByCustomerPage() {
  const [rows, setRows] = useState<any[]>([]);

  // State for search dialog and table visibility
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTableAfterSearch, setShowTableAfterSearch] = useState(false);

  // Use the common form state hook
  const {
    showForm,
    formMode,
    selectedObj: currentObj,
    handleCloseForm,
    handleViewClick,
    handleEditClick,
    handleAddClick
  } = useDialogState();

  const { selectedObj, handleRowClick } = useRowSelection();

  // Define tables for AritoDataTables
  const tables = [
    {
      name: 'Bảng kê chứng từ',
      rows: rows,
      columns: getDocumentsListColumns(handleViewClick, handleEditClick),
      value: 0
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {/* Initial Search Dialog */}
      <SearchDialog
        open={initialSearchDialogOpen || showForm}
        onClose={handleCloseForm}
        onSubmit={data => {
          setShowTableAfterSearch(true);
          setInitialSearchDialogOpen(false);
          handleCloseForm();
        }}
      />

      {/* Main Content */}
      {showTableAfterSearch && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onRefreshClick={() => {}}
            onExportDataClick={() => {}}
            onFixedColumnsClick={() => {}}
          />
          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} defaultTabIndex={0} />
          </div>
        </>
      )}
    </div>
  );
}
