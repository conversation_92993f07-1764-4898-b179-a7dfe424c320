"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page",{

/***/ "(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BasicInfoTab: function() { return /* binding */ BasicInfoTab; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _cols_definition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cols-definition */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/cols-definition.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2.tsx\");\n/* harmony import */ var _components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm */ \"(app-pages-browser)/./src/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm.tsx\");\n/* harmony import */ var _components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom/arito/form/search-fields */ \"(app-pages-browser)/./src/components/custom/arito/form/search-fields/index.ts\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/Tabs.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/Tabs.tsx\");\n/* harmony import */ var _components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom/arito/form/radio-button */ \"(app-pages-browser)/./src/components/custom/arito/form/radio-button/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom/arito/form/form */ \"(app-pages-browser)/./src/components/custom/arito/form/form/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BasicInfoTab = (param)=>{\n    let { formMode, setShowTaxCodePopupForm, showTaxCodePopupForm } = param;\n    _s();\n    const [formType, setFormType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chung_tu\");\n    const { control } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__.AritoFormContext);\n    const isPaymentChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch)({\n        control,\n        name: \"payment\",\n        defaultValue: false\n    });\n    const isDiscountChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch)({\n        control,\n        name: \"discount\",\n        defaultValue: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-3 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 space-x-2 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-[50px,1fr] items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm\",\n                                                children: \"Giao dịch\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__.RadioButton, {\n                                                name: \"dataType\",\n                                                options: [\n                                                    {\n                                                        value: \"service\",\n                                                        label: \"DV. Mua dịch vụ\"\n                                                    },\n                                                    {\n                                                        value: \"asset\",\n                                                        label: \"TS. Mua t\\xe0i sản/c\\xf4ng cụ\"\n                                                    }\n                                                ],\n                                                defaultValue: \"service\",\n                                                orientation: \"horizontal\",\n                                                onChange: ()=>{}\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-row\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            style: {\n                                                                width: \"160px\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    className: \"mr-2\",\n                                                                    type: \"checkbox\",\n                                                                    name: \"payment\",\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 64,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                !isPaymentChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Chi tiền\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 65,\n                                                                    columnNumber: 43\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        isPaymentChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                            type: \"select\",\n                                                            name: \"paymentMethod\",\n                                                            className: \"-ml-16 -mt-2 flex w-28 items-center\",\n                                                            options: [\n                                                                {\n                                                                    value: \"cash\",\n                                                                    label: \"Tiền mặt\"\n                                                                },\n                                                                {\n                                                                    value: \"bank\",\n                                                                    label: \"Chuyển khoản\"\n                                                                },\n                                                                {\n                                                                    value: \"credit\",\n                                                                    label: \"Kh\\xe1c\"\n                                                                }\n                                                            ],\n                                                            disabled: formMode === \"view\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            style: {\n                                                                width: \"160px\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    className: \"-ml-6\",\n                                                                    type: \"checkbox\",\n                                                                    name: \"discount\",\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Chiết khấu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 86,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        isDiscountChecked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"-ml-16 -mt-3 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    type: \"select\",\n                                                                    name: \"discountType\",\n                                                                    className: \"w-48\",\n                                                                    options: [\n                                                                        {\n                                                                            value: \"byHand\",\n                                                                            label: \"Tự nhập\"\n                                                                        },\n                                                                        {\n                                                                            value: \"percent\",\n                                                                            label: \"Giảm % theo h\\xf3a đơn\"\n                                                                        },\n                                                                        {\n                                                                            value: \"fixed\",\n                                                                            label: \"Giảm tiền tr\\xean tổng h\\xf3a đơn\"\n                                                                        }\n                                                                    ],\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 91,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                                    type: \"text\",\n                                                                    name: \"discountValue\",\n                                                                    className: \"w-12\",\n                                                                    disabled: formMode === \"view\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                                    lineNumber: 105,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-x-20 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid grid-cols-[160px,1fr] items-center\",\n                                        type: \"text\",\n                                        label: \"M\\xe3 nh\\xe0 cung cấp\",\n                                        name: \"supplierCode\",\n                                        disabled: formMode === \"view\",\n                                        withSearch: true,\n                                        searchEndpoint: \"accounting/supplier\",\n                                        searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNCCSearchColBasicInfo,\n                                        actionButtons: [\n                                            \"add\",\n                                            \"edit\"\n                                        ],\n                                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            formMode: formMode\n                                        }, void 0, false, void 0, void 0),\n                                        tabs: (0,_components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__.Type1Tabs)({\n                                            formMode\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                                type: \"text\",\n                                                label: \"M\\xe3 số thuế\",\n                                                name: \"taxCode\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 15\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                onClick: ()=>setShowTaxCodePopupForm(true),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 58\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[160px,1fr] sm:items-center\",\n                                type: \"text\",\n                                label: \"T\\xean nh\\xe0 cung cấp\",\n                                name: \"supplierName\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"Địa chỉ\",\n                                name: \"address\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"M\\xe3 nh\\xe2n vi\\xean\",\n                                name: \"employeeCode\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNVSearchColBasicInfo,\n                                searchEndpoint: \"hr/employee\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"T\\xe0i khoản c\\xf3\",\n                                name: \"accountNumber\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/account\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.TKCoSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__.Type2Tabs)({\n                                    formMode\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"Diễn giải\",\n                                name: \"description\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center text-red-600 lg:grid-cols-[120px,1fr]\",\n                                label: \"Dư c\\xf4ng nợ\",\n                                type: \"number\",\n                                name: \"debt\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Người giao h\\xe0ng\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"deliveryPerson\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Email\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"email\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__.PaymentTermSelectField, {\n                                name: \"paymentTerm\",\n                                label: \"Hạn thanh to\\xe1n\",\n                                formMode: formMode === \"view\" ? \"view\" : formMode === \"edit\" ? \"edit\" : \"add\",\n                                labelClassName: \"w-32\",\n                                inputClassName: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                label: \"Số chứng từ\",\n                                withSearch: true,\n                                type: \"text\",\n                                name: \"bookNumber\",\n                                disabled: formMode === \"view\",\n                                searchEndpoint: \"accounting/transaction\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaCTSearchColBasicInfo\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y chứng từ\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"dhmCode\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Số h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"recipeNumber\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"K\\xfd hiệu\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"ky_hieu\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"nhd\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 items-center gap-x-4 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid w-3/4 grid-cols-[160px,1fr] items-center lg:grid-cols-[90px,1fr]\",\n                                        inputClassName: \"w-3/4\",\n                                        label: \"Ngoại tệ\",\n                                        name: \"foreignCurrency\",\n                                        type: \"select\",\n                                        disabled: formMode === \"view\",\n                                        options: [\n                                            {\n                                                value: \"VND\",\n                                                label: \"VND\"\n                                            },\n                                            {\n                                                value: \"USD\",\n                                                label: \"USD\"\n                                            },\n                                            {\n                                                value: \"EUR\",\n                                                label: \"EUR\"\n                                            },\n                                            {\n                                                value: \"JPY\",\n                                                label: \"JPY\"\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"flex-grow lg:ml-4\",\n                                        inputClassName: \"w-full\",\n                                        name: \"exchangeRate\",\n                                        type: \"text\",\n                                        disabled: formMode === \"view\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Trạng th\\xe1i\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"status\",\n                                type: \"select\",\n                                disabled: formMode === \"view\",\n                                options: [\n                                    {\n                                        value: \"create\",\n                                        label: \"Đ\\xe3 ghi sổ\"\n                                    },\n                                    {\n                                        value: \"pending\",\n                                        label: \"Chờ duyệt\"\n                                    },\n                                    {\n                                        value: \"approved\",\n                                        label: \"Chưa ghi sổ\"\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Dữ liệu được nhận\",\n                                className: \"grid grid-cols-[200px,1fr] items-center\",\n                                name: \"status\",\n                                type: \"checkbox\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            showTaxCodePopupForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                showTaxCodePopupForm: showTaxCodePopupForm,\n                setShowTaxCodePopupForm: setShowTaxCodePopupForm,\n                formMode: formMode,\n                currentObj: {}\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfoTab, \"009JD+HxJuQpJs08QjxCCt52q+A=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useWatch\n    ];\n});\n_c = BasicInfoTab;\nvar _c;\n$RefreshReg$(_c, \"BasicInfoTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx\n"));

/***/ })

});