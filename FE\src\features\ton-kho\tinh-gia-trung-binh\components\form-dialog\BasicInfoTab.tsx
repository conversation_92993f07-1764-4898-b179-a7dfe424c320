'use client';

import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { materialSearchColumns, unitSearchColumns } from '../../cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';
import NumberRangeDropdown from '../number-range-dropdown';
import { DonVi, VatTu } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Kỳ từ/đến</Label>
          <NumberRangeDropdown fromName='ky_tu' toName='ky_den' />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Năm</Label>
          <div className='w-[120px]'>
            <FormField type='number' name='nam' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mã vật tư</Label>
          <div className='w-[250px]'>
            <SearchField<VatTu>
              type='text'
              searchEndpoint='/materials'
              searchColumns={materialSearchColumns}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục vật tư'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              className='w-full'
              classNameRelatedField='w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Đơn vị</Label>
          <div className='w-[250px]'>
            <SearchField
              type='text'
              searchEndpoint='/entity-units'
              searchColumns={unitSearchColumns}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục đơn vị'
              columnDisplay='ma_unit'
              displayRelatedField='ten_unit'
              className='w-full'
              classNameRelatedField='w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tạo chênh lệch</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              name='tao_px'
              disabled={formMode === 'view'}
              options={[
                { label: 'Không tạo', value: '0' },
                { label: 'Tạo khi không còn tồn kho', value: '1' },
                { label: 'Tất cả các trường hợp', value: '2' },
                { label: 'Xóa tất cả phiếu xuất chênh lệch', value: '3' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Cập nhật giá</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              name='update_px'
              disabled={formMode === 'view'}
              options={[
                { label: 'Chỉ tính giá', value: '1' },
                { label: 'Tính và cập nhật giá vào kho', value: '2' },
                { label: 'Chỉ cập nhật giá vào kho', value: '3' }
              ]}
              defaultValue={'2'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Cách tính giá</Label>
          <div className='w-[250px]'>
            <FormField type='text' name='method' disabled={true} placeholder='Giá chung tất cả các kho' />
          </div>
        </div>
      </div>
    </div>
  );
};
