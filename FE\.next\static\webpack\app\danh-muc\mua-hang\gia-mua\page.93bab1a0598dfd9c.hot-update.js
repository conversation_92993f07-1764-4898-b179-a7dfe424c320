"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx":
/*!*************************************************************************************!*\
  !*** ./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var _constants___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\n\n\n\n\n\nconst BasicInfo = (param)=>{\n    let { formMode, searchFields } = param;\n    const { vatTu, setVatTu, donViTinh, setDonViTinh, nhaCungCap, setNhaCungCap, ngoaiTe, setNgoaiTe } = searchFields;\n    const isViewMode = formMode === \"view\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-h-[calc(100vh-150px)] overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 md:p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col gap-y-4 md:gap-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 md:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Giao dịch\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                        type: \"radio\",\n                                        name: \"giao_dich\",\n                                        options: [\n                                            {\n                                                value: \"service\",\n                                                label: \"DV. Mua dịch vụ\"\n                                            },\n                                            {\n                                                value: \"asset\",\n                                                label: \"TS. Mua t\\xe0i sản/c\\xf4ng cụ chi tiết\"\n                                            }\n                                        ],\n                                        disabled: isViewMode,\n                                        className: \"flex gap-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"M\\xe3 vật tư\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_vt\",\n                                    columnDisplay: \"ma_vt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.VAT_TU, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.vatTuSearchColumns,\n                                    dialogTitle: \"Danh mục vật tư\",\n                                    value: (vatTu === null || vatTu === void 0 ? void 0 : vatTu.ma_vt) || \"\",\n                                    onRowSelection: setVatTu,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Đơn vị t\\xednh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_dvt\",\n                                    columnDisplay: \"dvt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.DON_VI_TINH, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.dvtSearchColumns,\n                                    dialogTitle: \"Danh mục đơn vị t\\xednh\",\n                                    value: (donViTinh === null || donViTinh === void 0 ? void 0 : donViTinh.dvt) || \"\",\n                                    onRowSelection: setDonViTinh,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ng\\xe0y hiệu lực\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"date\",\n                                    name: \"ngay_hieu_luc\",\n                                    className: \"w-40\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Nh\\xe0 cung cấp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"customer_name\",\n                                    columnDisplay: \"customer_code\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NHA_CUNG_CAP, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.khachHangSearchColumns,\n                                    dialogTitle: \"Danh mục nh\\xe0 cung cấp\",\n                                    value: (nhaCungCap === null || nhaCungCap === void 0 ? void 0 : nhaCungCap.customer_code) || \"\",\n                                    onRowSelection: setNhaCungCap,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Ngoại tệ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                                    type: \"text\",\n                                    displayRelatedField: \"ten_nt\",\n                                    columnDisplay: \"ma_nt\",\n                                    searchEndpoint: \"/\".concat(_constants___WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.NGOAI_TE, \"/\"),\n                                    searchColumns: _constants___WEBPACK_IMPORTED_MODULE_1__.ngoaiTeSearchColumns,\n                                    dialogTitle: \"Danh mục ngoại tệ\",\n                                    value: (ngoaiTe === null || ngoaiTe === void 0 ? void 0 : ngoaiTe.ma_nt) || \"\",\n                                    onRowSelection: setNgoaiTe,\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Số lượng từ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"so_luong_tu\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Gi\\xe1 mua\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    type: \"number\",\n                                    name: \"gia_mua\",\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"trang_thai\",\n                                    className: \"mb-2 sm:mb-0 sm:w-40 sm:min-w-40\",\n                                    children: \"Trạng th\\xe1i\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_3__.FormField, {\n                                    id: \"trang_thai\",\n                                    type: \"select\",\n                                    name: \"trang_thai\",\n                                    options: [\n                                        {\n                                            label: \"1. C\\xf2n sử dụng\",\n                                            value: 1\n                                        },\n                                        {\n                                            label: \"0. Kh\\xf4ng sử dụng\",\n                                            value: 0\n                                        }\n                                    ],\n                                    disabled: isViewMode\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\danh-muc\\\\mua-hang\\\\gia-mua\\\\components\\\\form-dialog\\\\BasicInfo.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_c = BasicInfo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BasicInfo);\nvar _c;\n$RefreshReg$(_c, \"BasicInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/components/form-dialog/BasicInfo.tsx\n"));

/***/ })

});