"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/page",{

/***/ "(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BasicInfoTab: function() { return /* binding */ BasicInfoTab; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _cols_definition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cols-definition */ \"(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/cols-definition.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/BasicInfoTabType1.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2 */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2.tsx\");\n/* harmony import */ var _components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm */ \"(app-pages-browser)/./src/components/cac-loai-form/add-tax-code-popup/TaxCodePopUpForm.tsx\");\n/* harmony import */ var _components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom/arito/form/search-fields */ \"(app-pages-browser)/./src/components/custom/arito/form/search-fields/index.ts\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-1/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-1/Tabs.tsx\");\n/* harmony import */ var _components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/cac-loai-form/popup-form-type-2/Tabs */ \"(app-pages-browser)/./src/components/cac-loai-form/popup-form-type-2/Tabs.tsx\");\n/* harmony import */ var _components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom/arito/form/radio-button */ \"(app-pages-browser)/./src/components/custom/arito/form/radio-button/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom/arito/form/form */ \"(app-pages-browser)/./src/components/custom/arito/form/form/index.tsx\");\n/* harmony import */ var _components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/custom/arito/form/form-field */ \"(app-pages-browser)/./src/components/custom/arito/form/form-field/index.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BasicInfoTab = (param)=>{\n    let { formMode, setShowTaxCodePopupForm, showTaxCodePopupForm } = param;\n    _s();\n    const [formType, setFormType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chung_tu\");\n    const { control } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_custom_arito_form_form__WEBPACK_IMPORTED_MODULE_10__.AritoFormContext);\n    const isPaymentChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch)({\n        control,\n        name: \"payment\",\n        defaultValue: false\n    });\n    const isDiscountChecked = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch)({\n        control,\n        name: \"discount\",\n        defaultValue: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-x-8 lg:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                        className: \"w-32 text-sm font-medium\",\n                                        children: \"Giao dịch\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_radio_button__WEBPACK_IMPORTED_MODULE_9__.RadioButton, {\n                                            name: \"dataType\",\n                                            options: transactionTypeOptions,\n                                            defaultValue: \"service\",\n                                            orientation: \"horizontal\",\n                                            onChange: ()=>{},\n                                            className: \"gap-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-8 pl-36\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"checkbox\",\n                                                name: \"payment\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                                className: \"text-sm\",\n                                                children: \"Chi tiền\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                type: \"checkbox\",\n                                                name: \"discount\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_13__.Label, {\n                                                className: \"text-sm\",\n                                                children: \"Chiết khấu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-x-20 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid grid-cols-[160px,1fr] items-center\",\n                                        type: \"text\",\n                                        label: \"M\\xe3 nh\\xe0 cung cấp\",\n                                        name: \"supplierCode\",\n                                        disabled: formMode === \"view\",\n                                        withSearch: true,\n                                        searchEndpoint: \"accounting/supplier\",\n                                        searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNCCSearchColBasicInfo,\n                                        actionButtons: [\n                                            \"add\",\n                                            \"edit\"\n                                        ],\n                                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_1_BasicInfoTabType1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            formMode: formMode\n                                        }, void 0, false, void 0, void 0),\n                                        tabs: (0,_components_cac_loai_form_popup_form_type_1_Tabs__WEBPACK_IMPORTED_MODULE_7__.Type1Tabs)({\n                                            formMode\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                                type: \"text\",\n                                                label: \"M\\xe3 số thuế\",\n                                                name: \"taxCode\",\n                                                disabled: formMode === \"view\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 15\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer\",\n                                                onClick: ()=>setShowTaxCodePopupForm(true),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_12__.AritoIcon, {\n                                                    icon: 58\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[160px,1fr] sm:items-center\",\n                                type: \"text\",\n                                label: \"T\\xean nh\\xe0 cung cấp\",\n                                name: \"supplierName\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"Địa chỉ\",\n                                name: \"address\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"M\\xe3 nh\\xe2n vi\\xean\",\n                                name: \"employeeCode\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaNVSearchColBasicInfo,\n                                searchEndpoint: \"hr/employee\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"T\\xe0i khoản c\\xf3\",\n                                name: \"accountNumber\",\n                                type: \"text\",\n                                disabled: formMode === \"view\",\n                                withSearch: true,\n                                searchEndpoint: \"accounting/account\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.TKCoSearchColBasicInfo,\n                                actionButtons: [\n                                    \"add\",\n                                    \"edit\"\n                                ],\n                                headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_popup_form_type_2_BasicInfoTabType2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    formMode: formMode\n                                }, void 0, false, void 0, void 0),\n                                tabs: (0,_components_cac_loai_form_popup_form_type_2_Tabs__WEBPACK_IMPORTED_MODULE_8__.Type2Tabs)({\n                                    formMode\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center\",\n                                label: \"Diễn giải\",\n                                name: \"description\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center text-red-600 lg:grid-cols-[120px,1fr]\",\n                                label: \"Dư c\\xf4ng nợ\",\n                                type: \"number\",\n                                name: \"debt\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Người giao h\\xe0ng\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"deliveryPerson\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Email\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"email\",\n                                type: \"text\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_search_fields__WEBPACK_IMPORTED_MODULE_6__.PaymentTermSelectField, {\n                                name: \"paymentTerm\",\n                                label: \"Hạn thanh to\\xe1n\",\n                                formMode: formMode === \"view\" ? \"view\" : formMode === \"edit\" ? \"edit\" : \"add\",\n                                labelClassName: \"w-32\",\n                                inputClassName: \"w-48\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                label: \"Số chứng từ\",\n                                withSearch: true,\n                                type: \"text\",\n                                name: \"bookNumber\",\n                                disabled: formMode === \"view\",\n                                searchEndpoint: \"accounting/transaction\",\n                                searchColumns: _cols_definition__WEBPACK_IMPORTED_MODULE_2__.MaCTSearchColBasicInfo\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y chứng từ\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"dhmCode\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Số h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"recipeNumber\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"K\\xfd hiệu\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"text\",\n                                name: \"ky_hieu\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Ng\\xe0y h\\xf3a đơn\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                type: \"date\",\n                                name: \"nhd\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 items-center gap-x-4 lg:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"grid w-3/4 grid-cols-[160px,1fr] items-center lg:grid-cols-[90px,1fr]\",\n                                        inputClassName: \"w-3/4\",\n                                        label: \"Ngoại tệ\",\n                                        name: \"foreignCurrency\",\n                                        type: \"select\",\n                                        disabled: formMode === \"view\",\n                                        options: [\n                                            {\n                                                value: \"VND\",\n                                                label: \"VND\"\n                                            },\n                                            {\n                                                value: \"USD\",\n                                                label: \"USD\"\n                                            },\n                                            {\n                                                value: \"EUR\",\n                                                label: \"EUR\"\n                                            },\n                                            {\n                                                value: \"JPY\",\n                                                label: \"JPY\"\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                        className: \"flex-grow lg:ml-4\",\n                                        inputClassName: \"w-full\",\n                                        name: \"exchangeRate\",\n                                        type: \"text\",\n                                        disabled: formMode === \"view\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Trạng th\\xe1i\",\n                                className: \"grid grid-cols-[160px,1fr] items-center lg:grid-cols-[120px,1fr]\",\n                                name: \"status\",\n                                type: \"select\",\n                                disabled: formMode === \"view\",\n                                options: [\n                                    {\n                                        value: \"create\",\n                                        label: \"Đ\\xe3 ghi sổ\"\n                                    },\n                                    {\n                                        value: \"pending\",\n                                        label: \"Chờ duyệt\"\n                                    },\n                                    {\n                                        value: \"approved\",\n                                        label: \"Chưa ghi sổ\"\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_form_field__WEBPACK_IMPORTED_MODULE_11__.FormField, {\n                                label: \"Dữ liệu được nhận\",\n                                className: \"grid grid-cols-[200px,1fr] items-center\",\n                                name: \"status\",\n                                type: \"checkbox\",\n                                disabled: formMode === \"view\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            showTaxCodePopupForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cac_loai_form_add_tax_code_popup_TaxCodePopUpForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                showTaxCodePopupForm: showTaxCodePopupForm,\n                setShowTaxCodePopupForm: setShowTaxCodePopupForm,\n                formMode: formMode,\n                currentObj: {}\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\StudyWork\\\\ERP\\\\FE\\\\src\\\\features\\\\mua-hang\\\\hoa-don-mua-vao\\\\hoa-don-mua-dich-vu\\\\components\\\\BasicInfoTab.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BasicInfoTab, \"009JD+HxJuQpJs08QjxCCt52q+A=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useWatch\n    ];\n});\n_c = BasicInfoTab;\nvar _c;\n$RefreshReg$(_c, \"BasicInfoTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/components/BasicInfoTab.tsx\n"));

/***/ })

});