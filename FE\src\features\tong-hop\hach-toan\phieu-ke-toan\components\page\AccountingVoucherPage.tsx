import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import Swal from 'sweetalert2';
import {
  accountingVoucherDetailColumns,
  accountingVoucherExpenseColumns,
  formDetailColumns,
  getAccountingVoucherColumns
} from '../../cols-definition';
import {
  expenseSchema,
  orderDetailSchema,
  orderSchema
} from '@/features/phai-tra/don-dat-mua/don-hang-mua-trong-nuoc/schemas';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import { BasicInfoTab } from '../BasicInfoTab';
import { DetailsTab } from '../DetailsTab';
import { ActionBar } from '../ActionBar';
import { BottomBar } from '../BottomBar';
import { TaxsTab } from '../TaxsTab';
export default function AccountingVoucherPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [rows, setOrderRows] = useState<any[]>(initialRows);
  //Add and Edit
  const [inputOrderDetail, setInputOrderDetail] = useState<any[]>([]);
  const [inputExpenseDetail, setInputExpenseDetail] = useState<any[]>([]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    if (obj.orderDetails) {
      setInputOrderDetail([...obj.orderDetails]);
    } else {
      setInputOrderDetail([]);
    }
    if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    if (obj.orderDetails) {
      setInputOrderDetail([...obj.orderDetails]);
    } else {
      setInputOrderDetail([]);
    }
    if (obj.orderExpense) {
      setInputExpenseDetail([...obj.orderExpense]);
    } else {
      setInputExpenseDetail([]);
    }
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputOrderDetail([]);
    setInputExpenseDetail([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    // console.log(inputOrderDetail);
    // console.log(inputExpenseDetail);
    console.log(data);
    try {
      // if (inputOrderDetail.length === 0) {
      //   Swal.fire({
      //     icon: "error",
      //     title: "Lỗi nhập liệu",
      //     html: `<p class="text-[15px]">Bạn chưa nhập chi tiết đơn hàng</p>`,
      //   });
      //   return;
      // }

      // Validate order detail
      for (const row of inputOrderDetail) {
        await orderDetailSchema.validate(row);
      }

      for (const row of inputExpenseDetail) {
        await expenseSchema.validate(row);
      }

      if (formMode === 'add') {
        const newId = Math.max(...rows.map(row => row.id || 0)) + 1;
        const newOrder = {
          ...data,
          id: newId,
          status: 'Chờ duyệt',
          statusHDDT: 'Chưa xuất',
          createdBy: 'Current User',
          createdAt:
            new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
          updatedBy: '',
          updatedAt: '',
          orderDetails: inputOrderDetail
        };

        setOrderRows([...rows, newOrder]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = rows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt:
                new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5),
              orderDetails: inputOrderDetail
            };
          }
          return row;
        });

        setOrderRows(updatedRows);
      }

      setShowForm(false);
      setInputOrderDetail([]);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi nhập liệu',
        html: `<p class="text-[15px]">${error.message}</p>`
      });
    }
  };

  const convertToDetails = (order: any) => {
    const details = order.orderItems.map((item: any) => ({
      ...item,
      productCode: item.productCode,
      productName: item.productName,
      unit: item.unit,
      warehouseCode: item.warehouseCode
    }));
    return details;
  };

  const handleRowClick = (params: GridRowParams) => {
    let order = params.row as any;
    if (order.orderItems) {
      const details = convertToDetails(order);
      order.orderDetails = details;
    }

    console.log(order);

    setSelectedObj(order);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm)
    },
    {
      name: 'Chưa ghi sổ',
      rows: rows.filter(row => row.status === 'Chưa ghi sổ'),
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='pink' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Chờ duyệt',
      rows: rows.filter(row => row.status === 'Chờ duyệt'),
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#EF4444' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    },
    {
      name: 'Đã ghi sổ',
      rows: rows.filter(row => row.statusHDDT === 'Đã duyệt'),
      columns: getAccountingVoucherColumns(handleOpenViewForm, handleOpenEditForm),
      icon: <AritoColoredDot color='#3B82F6' className='mr-2' />,
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            // schema={orderSchema}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'details',
                label: 'Chi tiết',
                component: <DetailsTab formMode={formMode} columns={formDetailColumns} />
              },
              {
                id: 'tax',
                label: 'Thuế',
                component: <TaxsTab formMode={formMode} columns={accountingVoucherExpenseColumns} />
              },
              {
                id: 'filekem',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            onClose={handleCloseForm}
            subTitle={'Phiếu kế toán'}
            bottomBar={<BottomBar totalQuantity={0} totalPayment={0} formMode={formMode} />}
          />
        </div>
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.orderDetails || []}
                columns={accountingVoucherDetailColumns}
                mode={formMode}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
