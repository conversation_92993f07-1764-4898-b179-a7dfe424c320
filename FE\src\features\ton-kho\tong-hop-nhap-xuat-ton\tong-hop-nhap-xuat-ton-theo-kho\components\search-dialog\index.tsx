import { But<PERSON> } from '@mui/material';
import React from 'react';
import { SearchFormValues, searchSchema, initialSearchValues } from '../../schema';
import { AritoHeaderTabs } from '@/components/custom/arito/header-tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';
import OtherTab from './OtherTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: SearchFormValues) => void;
  updateTableColumns?: (templateValue: string) => void;
  initialData?: SearchFormValues;
}

const SearchDialog: React.FC<SearchDialogProps> = ({
  openSearchDialog,
  onCloseSearchDialog,
  onSearch,
  updateTableColumns,
  initialData = initialSearchValues
}) => {
  const handleSubmit = (data: SearchFormValues) => {
    // Call the onSearch callback with the form data
    onSearch(data);

    // Close the dialog
    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Tổng hợp nhập xuất tồn theo giao dịch'
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'chi_tiet',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                },
                {
                  id: 'khac',
                  label: 'Khác',
                  component: <OtherTab formMode='add' />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
            <Button onClick={onCloseSearchDialog} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
