import React, { useState } from 'react';
import { Button } from '@mui/material';
import { z } from 'zod';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { availableFields } from '../../cols-definition';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

interface SaveTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: SaveTemplateFormData) => void;
  templateType: 'filter' | 'analysis';
}

const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

const SaveTemplateDialog: React.FC<SaveTemplateDialogProps> = ({ open, onClose, onSave, templateType }) => {
  const [selectedField, setSelectedField] = useState<string>('');

  const title = templateType === 'filter' ? 'Lưu mẫu báo cáo' : 'Lưu mới mẫu phân tích';

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='lg'
      actions={
        <>
          <Button
            variant='contained'
            type='submit'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>

          <Button variant='outlined' onClick={onClose}>
            <AritoIcon icon={885} marginX='4px' />
            Hủy
          </Button>
        </>
      }
    >
      <div className='max-h-[80vh] w-[800px] min-w-[800px] overflow-y-auto bg-gray-50'>
        <AritoForm
          mode='add'
          initialData={{
            templateName: '',
            templateName2: '',
            analysisTemplate: ''
          }}
          onSubmit={onSave}
          schema={saveTemplateSchema}
          hasAritoActionBar={false}
          tabs={
            <div className='w-full space-y-4'>
              <div className='grid w-full grid-cols-1 gap-4'>
                <div className='rounded-md bg-white p-4'>
                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên báo cáo:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName' type='text' className='w-full' />
                    </div>
                  </div>

                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên khác:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName2' type='text' className='w-full' />
                    </div>
                  </div>
                </div>

                {templateType === 'analysis' && (
                  <div className=''>
                    <div className='mb-4 border-b border-gray-200'>
                      <AritoHeaderTabs
                        tabs={[
                          {
                            id: 'analysis',
                            label: 'Mẫu phân tích',
                            component: <></>
                          }
                        ]}
                      />
                      <div className='mb-6 rounded-md bg-white p-4'>
                        <div className='grid grid-cols-3 gap-4'>
                          {/* Left Column */}
                          <div className='rounded-lg border bg-white p-4'>
                            <div className='mb-4 flex items-center gap-2'>
                              <div className='h-4 w-4'>
                                <AritoIcon icon={869} />
                              </div>
                              <span className='text-sm font-medium'>Cột phân tích</span>
                            </div>
                            <div className='space-y-2'>
                              {availableFields.map((field, index) => (
                                <div
                                  key={index}
                                  className={`cursor-pointer rounded border p-2 text-sm hover:bg-gray-50 ${
                                    selectedField === field ? 'bg-blue-100' : ''
                                  }`}
                                  onClick={() => setSelectedField(field)}
                                >
                                  {field}
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* You can add more columns here as needed */}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          }
        />
      </div>
    </AritoDialog>
  );
};

export default SaveTemplateDialog;
