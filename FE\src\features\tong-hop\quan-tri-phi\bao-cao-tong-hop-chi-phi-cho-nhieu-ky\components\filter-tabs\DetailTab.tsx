import {
  accountSearchColumns,
  currencySearchColumns,
  customerSearchColumns,
  documentCodeSearchColumns
} from '../../search-cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';

export const DetailTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Tài khoản'
          name='account'
          type='text'
          withSearch={true}
          searchEndpoint='Account'
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='account_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Ghi nợ/có'
          name='type'
          options={[
            { label: 'Nợ', value: 1 },
            { label: 'Có', value: 2 },
            { label: 'Tất cả', value: 3 }
          ]}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Tài khoản đối ứng'
          name='counter_account'
          type='text'
          withSearch={true}
          searchEndpoint='Account'
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='account_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Mã khách hàng'
          name='customer_code'
          type='text'
          withSearch={true}
          searchEndpoint='Customer'
          searchColumns={customerSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='customer_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Ngoại tệ'
          name='foreign_currency'
          type='text'
          withSearch={true}
          searchEndpoint='Currency'
          searchColumns={currencySearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='currency_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Mã chứng từ'
          name='document_code'
          type='text'
          withSearch={true}
          searchEndpoint='DocumentCode'
          searchColumns={documentCodeSearchColumns}
          defaultSearchColumn='name'
          // displayRelatedField="currency_name"
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='text'
          label='Diễn giải'
          name='description'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Mẫu báo cáo'
          name='report_template'
          options={[
            { label: 'Mẫu tiền chuẩn', value: 'standard_currency' },
            { label: 'Mẫu ngoại tệ', value: 'foreign_currency' }
          ]}
        />
      </div>
    </div>
  </div>
);
