import { z } from 'zod';

export const formSchema = z.object({
  uuid: z.string().optional(),
  giao_dich: z.string().default('service'), // Thêm trường giao dịch
  ngay_hieu_luc: z.string(),
  so_luong_tu: z.union([z.number(), z.string()]),
  gia_mua: z.union([z.number(), z.string()]).optional(),
  trang_thai: z.number().default(1),

  // Các trường dữ liệu đối tượng (cho edit mode)
  ma_vat_tu_data: z.any().optional(),
  don_vi_tinh_data: z.any().optional(),
  nha_cung_cap_data: z.any().optional(),
  ngoai_te_data: z.any().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  uuid: undefined,
  giao_dich: 'service', // Gi<PERSON> trị mặc định cho giao dịch
  ngay_hieu_luc: '',
  so_luong_tu: 0,
  gia_mua: 0,
  trang_thai: 1,

  // Các trường dữ liệu đối tượng
  ma_vat_tu_data: null,
  don_vi_tinh_data: null,
  nha_cung_cap_data: null,
  ngoai_te_data: null
};
