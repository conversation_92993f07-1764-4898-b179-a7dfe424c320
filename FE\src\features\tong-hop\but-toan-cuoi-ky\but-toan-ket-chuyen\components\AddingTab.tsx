import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '../search-cols-definition';

export const AddingTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-0'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='number'
          label='Stt'
          name='stt'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Tên bút toán'
          name='journalEntryName'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='select'
          label='Loại kết chuyển'
          name='allocationType'
          disabled={formMode === 'view'}
          options={[
            { value: '1', label: '1. Từ tk có sang tk nợ' },
            { value: '2', label: '2. Từ tk nợ sang tk có' },
            { value: '3', label: '3. Kết chuyển lãi lỗ' },
            { value: '4', label: '4. Kết chuyển thuế' }
          ]}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Tài khoản nợ'
          name='debitAccount'
          disabled={formMode === 'view'}
          withSearch={formMode != 'view'}
          searchEndpoint='Account'
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='account_name'
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Tài khoản có'
          name='creditAccount'
          disabled={formMode === 'view'}
          withSearch={formMode != 'view'}
          searchEndpoint='Account'
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='account_name'
        />
        <FormField
          className='ml-[100px]'
          type='checkbox'
          label='Chỉ kết chuyển các ps chi tiết'
          name='onlyDetail'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
