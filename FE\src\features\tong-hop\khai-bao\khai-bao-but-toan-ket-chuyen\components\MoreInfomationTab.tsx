import {
  caseSearchColumns,
  contractSearchColumns,
  departmentSearchColumns,
  feeCodeSearchColumns
} from '../search-cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';

export const MoreInfomationTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Bộ phận'
          name='department'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Department'
          searchColumns={departmentSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='department_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Vụ việc'
          name='case'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Case'
          searchColumns={caseSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='case_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Hợp đồng'
          name='contract'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Contract'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='contract_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Khế ước'
          name='agreement'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Agreement'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='contract_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Mã phí'
          name='fee_code'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='FeeCode'
          searchColumns={feeCodeSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='fee_code_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Sản phẩm'
          name='product'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Product'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='product_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Lệnh sản xuất'
          name='productionOrder'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='ProductionOrder'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='production_order_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Khách hàng/nhà cc'
          name='customerOrSupplier'
          disabled={formMode === 'view'}
          withSearch={true}
          searchEndpoint='Customer'
          searchColumns={contractSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='customer_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Trạng thái'
          name='status'
          options={[
            { label: '1. Còn sử dụng', value: 1 },
            { label: '0. Không sử dụng', value: 0 }
          ]}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
