import { <PERSON>, Pin, Printer, Refresh<PERSON>w, FileText } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import { SearchFormValues } from '../schema';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  searchParams?: SearchFormValues;
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onPrintClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
}

/**
 * Format date range for display
 */
const formatDateRange = (searchParams?: SearchFormValues): string => {
  if (!searchParams) return '';

  const fromDate = new Date(searchParams.period_from).toLocaleDateString('vi-VN');
  const toDate = new Date(searchParams.period_to).toLocaleDateString('vi-VN');

  return `từ ngày ${fromDate} đến ngày ${toDate}`;
};

/**
 * ActionBar component for the Business Operation Report page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  searchParams,
  isViewDisabled = false,
  onSearchClick,
  onPrintClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onEditPrintTemplateClick
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Báo cáo kết quả hoạt động kinh doanh</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
          <span className='font-semibold'>{formatDateRange(searchParams)}</span>
        </span>
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}

    <AritoMenuButton
      title='In ấn'
      icon={Printer}
      items={[
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 0
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 0
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh (song ngữ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ (song ngữ)',
          icon: <AritoIcon icon={569} />,
          onClick: onPrintClick,
          group: 1
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 2
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 2
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh (song ngữ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 3
        },
        {
          title: 'Báo cáo kết quả hoạt động kinh doanh giữa niên độ (song ngữ)',
          icon: <AritoIcon icon={18} />,
          onClick: onPrintClick,
          group: 3
        }
      ]}
    />

    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onExportClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
