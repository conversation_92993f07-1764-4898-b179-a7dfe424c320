import { FormField } from '@/components/custom/arito/form';

export const InternalShipmentFormTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='lg:grid-cols-[2fr, 1fr] grid grid-cols-1 gap-x-8 lg:space-y-0'>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[2fr,1fr]'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='select'
          label='Trạng thái HĐĐT'
          name='status'
          disabled={formMode === 'view'}
          options={[{ value: 'not_used', label: 'Không sử dụng' }]}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Số chứng từ'
          name='so_chung_tu'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[2fr,1fr]'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Ph/th thanh toán'
          name='paymentMethod'
          disabled={formMode === 'view'}
          withSearch={true}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Ký hiệu'
          name='signal'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[2fr,1fr]'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Lệnh điều động'
          name='order'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Mẫu hoá đơn'
          name='invoicePattern'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='date'
        label='Ngày điều động'
        name='orderDate'
        disabled={formMode === 'view'}
      />
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Người điều động'
          name='orderPerson'
          disabled={formMode === 'view'}
        />
        <FormField
          className='grid grid-cols-[160px,1fr] items-center'
          type='text'
          label='Về việc'
          name='about'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Khách hàng v/c'
        name='customer'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Tên khách hàng v/c'
        name='customerName'
        disabled={formMode === 'view'}
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Số hợp đồng v/c'
        name='contractNumber'
        disabled={formMode === 'view'}
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Phương tiện v/c'
        name='vehicle'
        disabled={formMode === 'view'}
        withSearch={true}
      />
      <FormField
        className='grid grid-cols-[160px,1fr] items-center'
        type='text'
        label='Lý do hủy'
        name='cancelReason'
        disabled={formMode === 'view'}
      />
    </div>
  </div>
);
