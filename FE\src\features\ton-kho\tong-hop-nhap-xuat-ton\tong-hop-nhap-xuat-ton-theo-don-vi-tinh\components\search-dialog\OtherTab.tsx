import React from 'react';
import {
  giaoDichSearchColumns,
  itemSearchColumns,
  loSearchColumns,
  nhomVatTuSearchColumns,
  quyDoiDVTSearchColumns,
  taiKhoanSearchColumns,
  viTriSearchColumns
} from '../../cols-definition';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface OtherTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab: React.FC<OtherTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-4 p-4'>
      <FormField
        name='vt_ton'
        type='select'
        label='Xem vật tư'
        labelClassName='text-sm min-w-44'
        disabled={formMode === 'view'}
        options={[
          { label: 'Còn tồn', value: '0' },
          { label: 'Không còn tồn', value: '1' },
          { label: 'Tất cả', value: '2' }
        ]}
      />
      <FormField
        name='ps_dc'
        type='select'
        label='Tính ps điều chuyển'
        labelClassName='text-sm min-w-44'
        disabled={formMode === 'view'}
        options={[
          { label: 'Không tính phát sinh điều chuyển kho', value: '0' },
          { label: 'Tính phát sinh điều chuyển kho', value: '1' }
        ]}
      />
      {/* Mẫu lọc báo cáo */}
      <div className='flex items-center gap-4'>
        <Label className='w-40 min-w-40 text-sm font-medium'>Mẫu lọc báo cáo</Label>
        <div className='flex flex-1 items-center'>
          <div className='flex-1'>
            <FormField
              name='id_maubc'
              type='select'
              disabled={formMode === 'view'}
              options={[{ label: 'Báo cáo LCTT gián tiếp - Thông tư 200', value: 'BCLTTGT01' }]}
            />
          </div>
          <div className='ml-2 h-9 w-9 flex-shrink-0'>
            <RadixHoverDropdown
              iconNumber={624}
              items={[
                {
                  value: 'create',
                  label: 'Lưu mẫu mới',
                  icon: 7,
                  onClick: () => console.log('Create new report template')
                },
                {
                  value: 'edit',
                  label: 'Lưu đè vào mẫu đang chọn',
                  icon: 75,
                  onClick: () => console.log('Edit current report template')
                },
                {
                  value: 'delete',
                  label: 'Xóa mẫu đang chọn',
                  icon: 8,
                  onClick: () => console.log('Delete current report template')
                }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
