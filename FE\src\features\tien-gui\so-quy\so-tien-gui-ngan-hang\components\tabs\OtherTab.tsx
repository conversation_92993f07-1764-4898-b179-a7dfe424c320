import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

export default function OtherTab() {
  return (
    <>
      <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo</Label>
          <FormField
            name='detail'
            type='select'
            options={[{ value: 'no', label: 'Người dùng tự lọc' }]}
            className='w-96'
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích DL</Label>
          <FormField
            name='detail'
            type='select'
            options={[{ value: 'no', label: 'Không phân tích' }]}
            className='w-96'
          />
        </div>
      </div>
    </>
  );
}
