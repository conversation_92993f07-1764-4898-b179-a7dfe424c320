import React from 'react';
import { ChungTuSearchColumns, SoChungTuSearchColumns } from '../../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoTabProps {
  mode: 'add' | 'edit' | 'view';
}

export function BasicInfoTab({ mode }: BasicInfoTabProps) {
  return (
    <div className='flex gap-4 p-4'>
      <div className='flex flex-[4] flex-col gap-2'>
        <div className='w-1/3'>
          <FormField
            name='documentType'
            label='Loại c/từ'
            type='select'
            options={[{ value: '1', label: '1. Chứng từ ghi âm' }]}
            disabled={mode === 'view'}
            labelClassName='min-w-32'
          />
        </div>
        <FormField
          name='description'
          labelClassName='min-w-32'
          label='Diễn giải'
          type='text'
          className='w-full'
          disabled={mode === 'view'}
        />
        <div className='flex w-1/4 items-center'>
          <label className='mr-2 min-w-32 text-sm'>Chứng từ gốc</label>
          <SearchField
            name='originalDocument'
            className='flex-1'
            searchColumns={ChungTuSearchColumns()}
            searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}/`}
            disabled={mode === 'view'}
          />
        </div>
      </div>
      <div className='flex flex-1 flex-col gap-2'>
        <div className='flex w-1/4 items-center'>
          <label className='mr-2 min-w-32 text-sm'>Số chứng từ</label>
          <SearchField
            name='documentNumber'
            className='flex-1'
            searchColumns={SoChungTuSearchColumns()}
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/`}
            disabled={mode === 'view'}
          />
        </div>
        <FormField name='documentDate' label='Ngày chứng từ' type='date' disabled={mode === 'view'} />
        <div className='flex items-end gap-2'>
          <FormField
            name='currency'
            label='Ngoại tệ'
            type='select'
            options={[{ value: 'VND', label: 'VND' }]}
            disabled={mode === 'view'}
          />
          <FormField name='currencyRate' type='number' className='w-48' disabled={mode === 'view'} />
        </div>
        <FormField
          name='status'
          label='Trạng thái'
          type='select'
          options={[
            { value: 'posted', label: 'Đã ghi sổ' },
            { value: 'unposted', label: 'Chưa ghi sổ' }
          ]}
          disabled={mode === 'view'}
        />
        <FormField name='isReceived' label='Dữ liệu được nhận' type='checkbox' disabled={mode === 'view'} />
      </div>
    </div>
  );
}
