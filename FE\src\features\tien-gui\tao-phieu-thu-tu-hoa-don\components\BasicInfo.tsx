import { useFormContext } from 'react-hook-form';
import React from 'react';
import {
  customerSearchColumns,
  documentTypeSearchColumns,
  foreignCurrencySearchColumns,
  permissionSearchColumns
} from '../cols-definition';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

export const BasicInfo: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='space-y-4 p-4'>
      {/* Processing type */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>X<PERSON> lý</Label>
        <div className='w-1/2'>
          <FormField
            name='xu_ly'
            type='select'
            options={[
              { value: 'Tạo', label: 'Tạo' },
              { value: 'Xóa', label: 'Xóa' }
            ]}
          />
        </div>
      </div>

      {/* Document date range */}
      <div className='flex w-full items-center'>
        <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
        <div className='w-1/2'>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct_tu' toDateName='ngay_ct_den' />
        </div>
      </div>

      {/* Document number range */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Số c/từ(từ/đến)</Label>
        <div className='grid w-1/2 grid-cols-2 gap-2'>
          <FormField name='so_ct_tu' type='text' />
          <FormField name='so_ct_den' type='text' />
        </div>
      </div>

      {/* Document type */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Chứng từ</Label>
        <div className='flex w-full'>
          <div className='flex-1'>
            <SearchField
              name='chung_tu'
              searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
              searchColumns={documentTypeSearchColumns}
              columnDisplay='ma_ct'
              displayRelatedField='ten_ct'
              onRowSelection={row => {
                setValue('chung_tu', row.ten_ct);
                setValue('chung_tu_code', row.ma_ct);
              }}
            />
          </div>
        </div>
      </div>

      {/* Customer code */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Mã khách hàng</Label>
        <div className='w-full'>
          <SearchField
            name='ma_khach_hang'
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
            searchColumns={customerSearchColumns}
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
          />
        </div>
      </div>

      {/* Foreign currency */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Ngoại tệ</Label>
        <div className='w-full'>
          <SearchField
            name='ngoai_te'
            searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}`}
            searchColumns={foreignCurrencySearchColumns}
            columnDisplay='ma_nt'
            displayRelatedField='ten_nt'
          />
        </div>
      </div>

      {/* Permission */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Quyển ct</Label>
        <div className='w-full'>
          <SearchField
            name='quyen_ct'
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
            searchColumns={permissionSearchColumns}
            columnDisplay='ten_quyen'
            displayRelatedField='ten_quyen'
          />
        </div>
      </div>

      {/* Department */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Đơn vị</Label>
        <div className='w-1/2'>
          <FormField
            name='don_vi'
            type='select'
            options={[
              {
                value: '0318423416',
                label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ'
              }
            ]}
          />
        </div>
      </div>

      {/* Filter by user */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Lọc theo người sd</Label>
        <div className='w-1/2'>
          <FormField
            name='loc_theo_nguoi_sd'
            type='select'
            options={[
              { value: 'Tất cả', label: 'Tất cả' },
              { value: 'Lọc theo người tạo', label: 'Lọc theo người tạo' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};
