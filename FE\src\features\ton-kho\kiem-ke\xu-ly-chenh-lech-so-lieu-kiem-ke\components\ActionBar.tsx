import { <PERSON><PERSON><PERSON><PERSON>, Pin, Refresh<PERSON>w, Search, Sheet, Table } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onSearchClick: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  className?: string;
  data?: any[];
}

const ActionBar: React.FC<ActionBarProps> = ({
  className,
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  data
}) => {
  const handleExport = () => {
    // Handle the export logic here
    console.log('Exporting data...');
  };

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'><PERSON>ảng kê nhập/xuất kho</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>Ngày 06/06/2023</span>
            </p>
          </div>
        </div>
      }
    >
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} />
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={handleExport} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
