import { objectSearchColumns, unitSearchColumns } from '../../search-cols-definition';
import { FormField } from '@/components/custom/arito/form/form-field';
export const DetailTab = () => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-4'>
      <div className='space-y-2'>
        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Mã đối tượng'
          name='object_code'
          withSearch={true}
          searchEndpoint='Object'
          searchColumns={objectSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='object_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Chi tiết dữ liệu'
          name='detail_data'
          options={[
            { label: '1. <PERSON> tiết theo phát sinh tài khoản', value: 1 },
            { label: '2. G<PERSON><PERSON> theo tài khoản đối ứng', value: 2 },
            { label: '3. Gộp cặp định khoản 1 dòng', value: 3 }
          ]}
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          label='Đơn vị'
          name='unit'
          withSearch={true}
          searchEndpoint='Unit'
          searchColumns={unitSearchColumns}
          defaultSearchColumn='name'
          displayRelatedField='unit_name'
        />

        <FormField
          className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
          type='select'
          label='Mẫu báo cáo'
          name='report_type'
          options={[
            { label: '1. Mẫu tiền chuẩn', value: 1 },
            { label: '2. Mẫu ngoại tệ', value: 2 }
          ]}
        />
      </div>
    </div>
  </div>
);
