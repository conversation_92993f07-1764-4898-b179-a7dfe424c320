import { useState, useEffect, useCallback } from 'react';
import { PhieuThu, PhieuThuChiTiet, PhieuThuInput, PhieuThuListResponse } from '@/types/schemas/phieu-thu.type';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UsePhieuThuReturn {
  phieuThus: PhieuThu[];
  isLoading: boolean;
  addPhieuThu: (newPhieuThu: PhieuThuInput) => Promise<void>;
  updatePhieuThu: (uuid: string, updatedPhieuThu: PhieuThuInput) => Promise<void>;
  deletePhieuThu: (uuid: string) => Promise<void>;
  refreshPhieuThus: () => Promise<void>;
  getPhieuThuById: (uuid: string) => Promise<PhieuThu | null>;
  getPhieuThuByCode: (code: string) => Promise<PhieuThu | null>;
  getPhieuThuByStatus: (status: string) => Promise<PhieuThu[]>;
  getPhieuThuByDateRange: (fromDate: string, toDate: string) => Promise<PhieuThu[]>;
  getPhieuThuByCustomer: (customerId: string) => Promise<PhieuThu[]>;
  // Chi tiet methods
  addPhieuThuChiTiet: (
    phieuThuUuid: string,
    chiTiet: Omit<PhieuThuChiTiet, 'uuid' | 'phieu_thu' | 'created' | 'updated'>
  ) => Promise<void>;
  updatePhieuThuChiTiet: (
    phieuThuUuid: string,
    chiTietUuid: string,
    chiTiet: Omit<PhieuThuChiTiet, 'uuid' | 'phieu_thu' | 'created' | 'updated'>
  ) => Promise<void>;
  deletePhieuThuChiTiet: (phieuThuUuid: string, chiTietUuid: string) => Promise<void>;
  getPhieuThuChiTiet: (phieuThuUuid: string) => Promise<PhieuThuChiTiet[]>;
}

/**
 * Hook for managing PhieuThu (Receipt Voucher) data
 *
 * This hook provides functions to fetch, create, update, and delete receipt vouchers.
 */
export const usePhieuThu = (initialPhieuThus: PhieuThu[] = []): UsePhieuThuReturn => {
  const [phieuThus, setPhieuThus] = useState<PhieuThu[]>(initialPhieuThus);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchPhieuThus = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<PhieuThuListResponse>(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/`);
      setPhieuThus(response.data.results);
    } catch (error) {
      console.error('Error fetching receipt vouchers:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  /**
   * Get PhieuThu by ID
   */
  const getPhieuThuById = async (uuid: string): Promise<PhieuThu | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get<PhieuThu>(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching receipt voucher with UUID ${uuid}:`, error);
      return null;
    }
  };

  const getPhieuThuByCode = async (code: string): Promise<PhieuThu | null> => {
    if (!entity?.slug) return null;

    setIsLoading(true);
    try {
      const response = await api.get<PhieuThuListResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/?so_ct=${code}`
      );

      if (response.data.results.length > 0) {
        return response.data.results[0];
      }
      return null;
    } catch (error) {
      console.error('Error fetching receipt voucher by code:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get PhieuThus by status
   */
  const getPhieuThuByStatus = async (status: string): Promise<PhieuThu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<PhieuThuListResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/?status=${status}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching receipt vouchers by status:', error);
      return [];
    }
  };

  /**
   * Get PhieuThus by date range
   */
  const getPhieuThuByDateRange = async (fromDate: string, toDate: string): Promise<PhieuThu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<PhieuThuListResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/?from_date=${fromDate}&to_date=${toDate}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching receipt vouchers by date range:', error);
      return [];
    }
  };

  /**
   * Get PhieuThus by customer
   */
  const getPhieuThuByCustomer = async (customerId: string): Promise<PhieuThu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<PhieuThuListResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/?ma_kh=${customerId}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching receipt vouchers by customer:', error);
      return [];
    }
  };

  const addPhieuThu = async (newPhieuThu: PhieuThuInput): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.post<PhieuThu>(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/`, newPhieuThu);
      const addedPhieuThu: PhieuThu = response.data;
      setPhieuThus(prev => [...prev, addedPhieuThu]);
    } catch (error) {
      console.error('Error adding receipt voucher:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updatePhieuThu = async (uuid: string, updatedPhieuThu: PhieuThuInput): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.put<PhieuThu>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${uuid}/`,
        updatedPhieuThu
      );
      const updatedData: PhieuThu = response.data;
      setPhieuThus(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));
    } catch (error) {
      console.error('Error updating receipt voucher:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deletePhieuThu = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${uuid}/`);
      setPhieuThus(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting receipt voucher:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refresh PhieuThus data
   */
  const refreshPhieuThus = async (): Promise<void> => {
    await fetchPhieuThus();
  };

  // PhieuThu Chi Tiet Methods

  /**
   * Get PhieuThu Chi Tiet by PhieuThu UUID
   */
  const getPhieuThuChiTiet = async (phieuThuUuid: string): Promise<PhieuThuChiTiet[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<{ results: PhieuThuChiTiet[] }>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${phieuThuUuid}/chi-tiet/`
      );
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching receipt voucher details:', error);
      return [];
    }
  };

  /**
   * Add PhieuThu Chi Tiet
   */
  const addPhieuThuChiTiet = async (
    phieuThuUuid: string,
    chiTiet: Omit<PhieuThuChiTiet, 'uuid' | 'phieu_thu' | 'created' | 'updated'>
  ): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    try {
      await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${phieuThuUuid}/chi-tiet/`, chiTiet);
    } catch (error) {
      console.error('Error adding receipt voucher detail:', error);
      throw error;
    }
  };

  /**
   * Update PhieuThu Chi Tiet
   */
  const updatePhieuThuChiTiet = async (
    phieuThuUuid: string,
    chiTietUuid: string,
    chiTiet: Omit<PhieuThuChiTiet, 'uuid' | 'phieu_thu' | 'created' | 'updated'>
  ): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    try {
      await api.patch(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${phieuThuUuid}/chi-tiet/${chiTietUuid}/`,
        chiTiet
      );
    } catch (error) {
      console.error('Error updating receipt voucher detail:', error);
      throw error;
    }
  };

  /**
   * Delete PhieuThu Chi Tiet
   */
  const deletePhieuThuChiTiet = async (phieuThuUuid: string, chiTietUuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_THU}/${phieuThuUuid}/chi-tiet/${chiTietUuid}/`);
    } catch (error) {
      console.error('Error deleting receipt voucher detail:', error);
      throw error;
    }
  };

  // Load initial data
  useEffect(() => {
    fetchPhieuThus();
  }, [fetchPhieuThus]);

  return {
    phieuThus,
    isLoading,
    addPhieuThu,
    updatePhieuThu,
    deletePhieuThu,
    refreshPhieuThus,
    getPhieuThuById,
    getPhieuThuByCode,
    getPhieuThuByStatus,
    getPhieuThuByDateRange,
    getPhieuThuByCustomer,
    addPhieuThuChiTiet,
    updatePhieuThuChiTiet,
    deletePhieuThuChiTiet,
    getPhieuThuChiTiet
  };
};
