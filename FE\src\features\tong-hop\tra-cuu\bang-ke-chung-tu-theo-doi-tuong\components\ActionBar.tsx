import { Search, RefreshCw, Printer, Pin, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick?: () => void;
  onRefreshClick?: () => void;
  onExportDataClick?: () => void;
  onFixedColumnsClick?: () => void;
  className?: string;
}

export const ActionBar = ({
  onAddClick,
  onRefreshClick,
  onExportDataClick,
  onFixedColumnsClick,
  className
}: ActionBarProps) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON>ng kê chứng từ theo đối tượng</h1>}
    >
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onAddClick} />
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Mẫu chuẩn',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Mẫu ngoại tệ',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 0
          }
        ]}
      />
      <AritoActionButton title='Làm mới' icon={RefreshCw} onClick={onRefreshClick} />
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} />
      <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={() => {}} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onExportDataClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};
