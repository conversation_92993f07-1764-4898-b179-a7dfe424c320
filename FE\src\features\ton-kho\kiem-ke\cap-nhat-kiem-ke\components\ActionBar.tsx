import { <PERSON><PERSON><PERSON>, Plus, Trash, Co<PERSON>, <PERSON>Search, Refresh<PERSON><PERSON>, Pin, TextSearch, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onSearchClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onViewClick?: () => void;
  onCopyClick?: () => void;
  onExportClick?: () => void;
  onFixedColumnsClick?: () => void;
  onRefreshClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  className?: string;
  data?: any[];
}

const ActionBar: React.FC<ActionBarProps> = ({
  className,
  onAddClick,
  onSearchClick,
  onEditClick,
  onDeleteClick,
  onViewClick,
  onCopyClick,
  onExportClick,
  onFixedColumnsClick,
  onRefreshClick,
  isEditDisabled = true,
  isViewDisabled = true,
  data
}) => {
  const handleExport = () => {
    // Handle the export logic here
    console.log('Exporting data...');
  };

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Cập nhật dữ liệu kiểm kê</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>Phiếu kiểm kê 24 kho [BAR0] ngày 06/06/2023</span>
            </p>
          </div>
        </div>
      }
    >
      {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
      {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />}
      {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} />}
      {onCopyClick && <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} />}
      {onViewClick && <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} />}
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Lọc phiếu kiểm kê',
            icon: <AritoIcon icon={12} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: onFixedColumnsClick,
            group: 1
          },
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 2
          },
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: () => {},
            group: 2
          },

          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportClick,
            group: 3
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 3
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 3
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
