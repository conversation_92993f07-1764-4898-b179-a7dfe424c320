import { Search, RefreshCcw, Pin } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

// Define the type for search parameters
interface SearchParams {
  ky_tu?: number;
  ky_den?: number;
  nam?: number;
  [key: string]: any; // Allow for other properties
}

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintClick?: () => void;
  searchParams?: SearchParams | null;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportClick,
  onEditPrintClick,
  searchParams
}) => {
  // Default values if searchParams is not provided
  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();

  // Use searchParams if available, otherwise use defaults
  const displayKyTu = searchParams?.ky_tu || currentMonth;
  const displayKyDen = searchParams?.ky_den || currentMonth;
  const displayNam = searchParams?.nam || currentYear;

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Bảng giá trung bình</h1>
          <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
            <div className='mr-2 size-2 rounded-full bg-red-500' />
            Từ kỳ {displayKyTu} đến {displayKyDen} năm {displayNam}
          </span>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tính điều kiện khác' icon={Search} onClick={onSearchClick} />}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCcw} onClick={onRefreshClick} disabled={isViewDisabled} />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
      )}

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintClick
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportClick
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
