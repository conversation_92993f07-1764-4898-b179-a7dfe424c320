import { But<PERSON> } from '@mui/material';
import React from 'react';
import { SearchFormValues, searchSchema, initialSearchValues } from '../../schema';
import { AritoHeaderTabs, BottomBar } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (data: SearchFormValues) => void;
}

const SearchDialog: React.FC<SearchDialogProps> = ({ open, onClose, onSearch }) => {
  const handleSubmit = (data: SearchFormValues) => {
    // Call the onSearch callback with the form data
    onSearch(data);

    // Close the dialog
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='<PERSON><PERSON><PERSON> c<PERSON><PERSON> lưu chuyển tiền tệ trực tiếp'
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail_tab',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar onClose={onClose} mode='add' />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
